#!/usr/bin/env python3
"""
检查combined_data.json.gz文件的logo状态
"""

import os
import gzip
import json
from pathlib import Path

def check_combined_data_file(file_path):
    """
    检查单个combined_data.json.gz文件的authorized状态
    
    Args:
        file_path (str): combined_data.json.gz文件的路径
    
    Returns:
        dict: 文件信息
    """
    try:
        with gzip.open(file_path, 'rt', encoding='UTF-8') as f:
            combined_data = json.load(f)
        
        return {
            'file_path': file_path,
            'uid': combined_data.get('uid', 'N/A'),
            'frame_num': combined_data.get('frame_num', 'N/A'),
            'authorized': combined_data.get('authorized', False),
            'status': 'success'
        }
    except Exception as e:
        return {
            'file_path': file_path,
            'uid': 'N/A',
            'frame_num': 'N/A',
            'authorized': 'ERROR',
            'status': f'error: {e}'
        }

def find_combined_data_files(root_dir="."):
    """
    查找所有的combined_data.json.gz文件
    
    Args:
        root_dir (str): 搜索的根目录
    
    Returns:
        list: 找到的文件路径列表
    """
    combined_data_files = []
    
    for root, dirs, files in os.walk(root_dir):
        for file in files:
            if file == "combined_data.json.gz":
                file_path = os.path.join(root, file)
                combined_data_files.append(file_path)
    
    return combined_data_files

def main():
    """主函数"""
    print("=== 数字人形象Logo状态检查工具 ===")
    print("正在搜索combined_data.json.gz文件...")
    
    # 查找所有combined_data.json.gz文件
    files = find_combined_data_files()
    
    if not files:
        print("未找到任何combined_data.json.gz文件")
        return
    
    print(f"找到 {len(files)} 个文件:\n")
    
    # 检查每个文件的状态
    results = []
    for file_path in files:
        result = check_combined_data_file(file_path)
        results.append(result)
    
    # 显示结果
    print("文件状态报告:")
    print("=" * 80)
    print(f"{'文件路径':<40} {'授权状态':<10} {'帧数':<8} {'UID':<20}")
    print("-" * 80)
    
    authorized_count = 0
    unauthorized_count = 0
    error_count = 0
    
    for result in results:
        status_display = "✓ 已授权" if result['authorized'] is True else "✗ 未授权" if result['authorized'] is False else "❌ 错误"
        
        if result['authorized'] is True:
            authorized_count += 1
        elif result['authorized'] is False:
            unauthorized_count += 1
        else:
            error_count += 1
        
        print(f"{result['file_path']:<40} {status_display:<10} {result['frame_num']:<8} {str(result['uid'])[:20]:<20}")
        
        if result['status'] != 'success':
            print(f"    错误: {result['status']}")
    
    print("-" * 80)
    print(f"总计: {len(files)} 个文件")
    print(f"已授权(无logo): {authorized_count} 个")
    print(f"未授权(有logo): {unauthorized_count} 个")
    print(f"错误文件: {error_count} 个")
    
    if unauthorized_count > 0:
        print(f"\n⚠️  发现 {unauthorized_count} 个文件仍显示logo")
        print("建议运行以下命令去除logo:")
        print("  python remove_logo_batch.py")
        print("或者单独处理:")
        for result in results:
            if result['authorized'] is False:
                print(f"  python remove_logo_single.py \"{result['file_path']}\"")
    else:
        print("\n🎉 所有文件都已去除logo！")

if __name__ == "__main__":
    main()
