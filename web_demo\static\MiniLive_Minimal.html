<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>精简版数字人 - 自定义嘴唇同步</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            font-family: 'Arial', sans-serif;
            color: white;
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            text-align: center;
        }
        
        h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .subtitle {
            font-size: 1.2em;
            margin-bottom: 30px;
            opacity: 0.9;
        }
        
        .video-container {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 15px;
            padding: 20px;
            margin: 20px auto;
            display: inline-block;
            box-shadow: 0 8px 32px rgba(0,0,0,0.3);
        }
        
        #canvas_video {
            border: 2px solid rgba(255, 255, 255, 0.2);
            border-radius: 10px;
            background: #000;
            max-width: 100%;
            height: auto;
        }
        
        .controls {
            margin-top: 20px;
            display: flex;
            justify-content: center;
            gap: 15px;
            flex-wrap: wrap;
        }
        
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 25px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }
        
        .btn-primary {
            background: linear-gradient(45deg, #28a745, #20c997);
            color: white;
        }
        
        .btn-danger {
            background: linear-gradient(45deg, #dc3545, #fd7e14);
            color: white;
        }
        
        .btn-info {
            background: linear-gradient(45deg, #17a2b8, #6f42c1);
            color: white;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0,0,0,0.3);
        }
        
        .status-panel {
            background: rgba(0, 0, 0, 0.4);
            border-radius: 10px;
            padding: 15px;
            margin: 20px auto;
            max-width: 600px;
            text-align: left;
        }
        
        .status-item {
            display: flex;
            justify-content: space-between;
            margin: 8px 0;
            padding: 5px 0;
            border-bottom: 1px solid rgba(255,255,255,0.1);
        }
        
        .status-label {
            font-weight: bold;
        }
        
        .status-value {
            font-family: monospace;
        }
        
        .feature-list {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 20px;
            margin: 20px auto;
            max-width: 800px;
            text-align: left;
        }
        
        .feature-list h3 {
            margin-top: 0;
            color: #ffd700;
        }
        
        .feature-list ul {
            list-style: none;
            padding: 0;
        }
        
        .feature-list li {
            margin: 10px 0;
            padding-left: 25px;
            position: relative;
        }
        
        .feature-list li:before {
            content: "✨";
            position: absolute;
            left: 0;
        }
        
        @media (max-width: 768px) {
            .controls {
                flex-direction: column;
                align-items: center;
            }
            
            .btn {
                width: 200px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎤 精简版数字人系统</h1>
        <p class="subtitle">自定义音频驱动嘴唇同步 - 无Logo版本</p>
        
        <div class="video-container">
            <canvas id="canvas_video" width="512" height="512"></canvas>
        </div>
        
        <div class="controls">
            <button class="btn btn-primary" onclick="startCustomLipSync()">
                🎤 启动嘴唇同步
            </button>
            <button class="btn btn-danger" onclick="stopCustomLipSync()">
                ⏹️ 停止同步
            </button>
            <button class="btn btn-info" onclick="showDebugInfo()">
                🔍 调试信息
            </button>
            <button class="btn btn-info" onclick="toggleOriginalSystem()">
                🔄 切换系统
            </button>
        </div>
        
        <div class="status-panel">
            <h3 style="margin-top: 0; color: #ffd700;">📊 系统状态</h3>
            <div class="status-item">
                <span class="status-label">WebGL渲染:</span>
                <span class="status-value" id="webgl-status">检测中...</span>
            </div>
            <div class="status-item">
                <span class="status-label">WASM模块:</span>
                <span class="status-value" id="wasm-status">检测中...</span>
            </div>
            <div class="status-item">
                <span class="status-label">自定义嘴唇:</span>
                <span class="status-value" id="lip-status">未启动</span>
            </div>
            <div class="status-item">
                <span class="status-label">音频权限:</span>
                <span class="status-value" id="audio-status">未请求</span>
            </div>
            <div class="status-item">
                <span class="status-label">当前模式:</span>
                <span class="status-value" id="mode-status">精简模式</span>
            </div>
        </div>
        
        <div class="feature-list">
            <h3>🎯 系统特点</h3>
            <ul>
                <li><strong>完全去除Logo:</strong> 基于WASM内存直接修改，彻底绕过授权检查</li>
                <li><strong>自定义嘴唇同步:</strong> 基于Web Audio API的实时音频分析</li>
                <li><strong>智能音素识别:</strong> 共振峰分析，支持中文发音特征</li>
                <li><strong>精简架构:</strong> 移除不必要的组件，专注核心功能</li>
                <li><strong>实时响应:</strong> 低延迟音频处理，自然流畅的嘴唇动画</li>
                <li><strong>可视化调试:</strong> 实时显示音频分析数据和系统状态</li>
            </ul>
        </div>
    </div>

    <!-- 必要的脚本文件 -->
    <script src="js/pako.min.js"></script>
    <script src="js/mp4box.all.min.js"></script>

    <!-- 配置Qt WebAssembly应用 -->
    <script>
        // Qt WebAssembly应用配置
        var Module = {
            locateFile: function(path, scriptDirectory) {
                console.log('🔍 locateFile:', path);
                if (path.endsWith('.wasm')) {
                    return 'DHLiveMini.wasm'; // WASM文件在static根目录
                }
                return scriptDirectory + path;
            },
            onRuntimeInitialized: function() {
                console.log('🎉 WASM运行时初始化完成');
            },
            print: function(text) {
                console.log('📝 WASM输出:', text);
            },
            printErr: function(text) {
                console.error('❌ WASM错误:', text);
            }
        };

        // Qt应用初始化函数
        async function initQtWebAssembly() {
            console.log('🚀 初始化Qt WebAssembly应用...');

            try {
                if (typeof createQtAppInstance === 'function') {
                    console.log('✅ 找到createQtAppInstance函数');

                    // 创建Qt应用实例
                    const qtApp = await createQtAppInstance(Module);
                    console.log('✅ Qt应用实例创建成功');

                    // 更新全局Module对象
                    if (qtApp) {
                        window.Module = qtApp;
                        console.log('✅ 全局Module对象已更新');

                        // 等待ready Promise
                        if (qtApp.ready) {
                            await qtApp.ready;
                            console.log('✅ Qt应用ready Promise已解决');
                        }

                        // 触发WASM助手检查
                        if (window.wasmHelper) {
                            setTimeout(() => {
                                console.log('🔍 触发WASM状态检查');
                            }, 100);
                        }
                    }

                    return qtApp;
                } else {
                    console.error('❌ 未找到createQtAppInstance函数');
                    return null;
                }
            } catch (error) {
                console.error('❌ Qt应用初始化失败:', error);
                return null;
            }
        }
    </script>
    <script src="js/DHLiveMini.js"></script>

    <!-- WASM加载助手 -->
    <script src="js/wasm_loader_helper.js"></script>

    <!-- 自定义系统脚本 -->
    <script src="js/wasm_memory_bypass.js"></script>
    <script src="js/custom_lip_sync.js"></script>
    <script src="js/visual_digital_human.js"></script>
    <script src="js/minimal_system.js"></script>

    <script>
        // 初始化完整的数字人系统
        async function initCompleteSystem() {
            console.log('🚀 初始化完整数字人系统...');

            try {
                updateStatus('mode-status', '初始化中...', '#ffc107');

                // 初始化可视化数字人系统
                if (window.visualDigitalHuman) {
                    const success = window.visualDigitalHuman.init();
                    if (!success) {
                        throw new Error('可视化数字人系统初始化失败');
                    }
                    console.log('✅ 可视化数字人系统初始化成功');
                }

                // 初始化精简系统（如果需要WASM功能）
                if (window.minimalSystem) {
                    await window.minimalSystem.init();
                    console.log('✅ 精简系统初始化成功');
                }

                updateStatus('mode-status', '可视化模式', '#17a2b8');
                console.log('✅ 完整系统初始化成功');
                return true;
            } catch (error) {
                console.error('❌ 系统初始化失败:', error);
                updateStatus('mode-status', '初始化失败', '#dc3545');
                return false;
            }
        }

        // 全局控制函数
        async function startCustomLipSync() {
            console.log('🚀 启动自定义嘴唇同步');

            if (!window.customLipSync) {
                alert('❌ 自定义嘴唇同步系统未加载');
                return;
            }

            try {
                // 先初始化完整系统
                const systemInitialized = await initCompleteSystem();
                if (!systemInitialized) {
                    alert('❌ 系统初始化失败');
                    return;
                }

                // 启动自定义嘴唇同步
                const success = await window.customLipSync.start();
                if (success) {
                    updateStatus('lip-status', '✅ 运行中', '#28a745');
                    updateStatus('audio-status', '✅ 已授权', '#28a745');
                    alert('✅ 自定义嘴唇同步已启动！请对着麦克风说话测试效果。');
                } else {
                    updateStatus('lip-status', '❌ 启动失败', '#dc3545');
                    alert('❌ 启动失败，请检查麦克风权限');
                }
            } catch (error) {
                console.error('启动失败:', error);
                alert('❌ 启动失败: ' + error.message);
            }
        }
        
        function stopCustomLipSync() {
            console.log('⏹️ 停止自定义嘴唇同步');
            
            if (window.customLipSync) {
                window.customLipSync.stop();
                updateStatus('lip-status', '⏹️ 已停止', '#ffc107');
                alert('⏹️ 自定义嘴唇同步已停止');
            }
        }
        
        function showDebugInfo() {
            if (window.customLipSync) {
                const debug = window.customLipSync.getDebugInfo();
                const info = `
🔍 调试信息:
━━━━━━━━━━━━━━━━━━━━
📊 音频分析:
  • 音量: ${debug.volume}
  • 频率: ${debug.frequency}Hz
  • 音素: ${debug.phoneme}
  • 共振峰: ${debug.formants}个

🎭 嘴唇形状:
  • 当前参数: [${debug.currentShape.join(', ')}]

🔧 系统状态:
  • 活跃状态: ${debug.isActive ? '✅' : '❌'}
  • WebGL: ${window.MinimalSystem?.gl ? '✅' : '❌'}
  • WASM: ${typeof Module !== 'undefined' ? '✅' : '❌'}
━━━━━━━━━━━━━━━━━━━━
                `.trim();
                
                alert(info);
                console.log('🔍 详细调试信息:', debug);
            } else {
                alert('❌ 调试信息不可用');
            }
        }
        
        let isOriginalMode = false;
        function toggleOriginalSystem() {
            if (isOriginalMode) {
                // 切换到精简模式
                if (window.minimalSystem) {
                    window.minimalSystem.init();
                    updateStatus('mode-status', '精简模式', '#17a2b8');
                    isOriginalMode = false;
                    alert('🎯 已切换到精简模式');
                }
            } else {
                // 切换到原始模式
                if (window.minimalSystem) {
                    window.minimalSystem.restore();
                    updateStatus('mode-status', '原始模式', '#ffc107');
                    isOriginalMode = true;
                    alert('🔄 已切换到原始模式');
                }
            }
        }
        
        function updateStatus(elementId, text, color = '#ccc') {
            const element = document.getElementById(elementId);
            if (element) {
                element.textContent = text;
                element.style.color = color;
            }
        }
        
        // 定期更新状态
        setInterval(() => {
            // WebGL状态
            const webglOk = window.MinimalSystem?.gl;
            updateStatus('webgl-status', webglOk ? '✅ 正常' : '❌ 未初始化', webglOk ? '#28a745' : '#dc3545');
            
            // WASM状态
            const wasmOk = typeof Module !== 'undefined' && Module._updateBlendShape;
            updateStatus('wasm-status', wasmOk ? '✅ 已加载' : '❌ 未加载', wasmOk ? '#28a745' : '#dc3545');
            
            // 嘴唇同步状态
            if (window.customLipSync && window.customLipSync.isActive) {
                updateStatus('lip-status', '✅ 运行中', '#28a745');
            } else if (document.getElementById('lip-status').textContent === '检测中...') {
                updateStatus('lip-status', '⏸️ 未启动', '#ffc107');
            }
        }, 1000);
        
        // 页面加载完成后初始化Qt应用
        window.addEventListener('load', async () => {
            console.log('📋 页面加载完成，开始初始化...');

            // 等待一段时间确保所有脚本都已加载
            setTimeout(async () => {
                // 初始化Qt WebAssembly应用
                const qtApp = await initQtWebAssembly();

                if (qtApp) {
                    console.log('🎉 Qt WebAssembly应用初始化成功！');
                    updateStatus('mode-status', '系统就绪', '#28a745');
                } else {
                    console.error('❌ Qt WebAssembly应用初始化失败');
                    updateStatus('mode-status', '初始化失败', '#dc3545');
                }

                console.log('💡 使用说明:');
                console.log('  1. 等待右下角WASM状态显示全部✅');
                console.log('  2. 点击"启动嘴唇同步"开始音频驱动');
                console.log('  3. 允许麦克风权限');
                console.log('  4. 对着麦克风说话测试效果');
                console.log('  5. 使用"调试信息"查看实时数据');
            }, 2000);
        });
    </script>
</body>
</html>
