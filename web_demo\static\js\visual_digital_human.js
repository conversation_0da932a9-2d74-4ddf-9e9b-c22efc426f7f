/**
 * 可视化数字人渲染系统
 * 提供清晰的视觉反馈和嘴唇同步显示
 */

console.log('👁️ 可视化数字人渲染系统已加载');

window.VisualDigitalHuman = {
    isInitialized: false,
    canvas: null,
    ctx: null,
    animationId: null,
    currentLipShape: null,
    faceModel: null
};

/**
 * 初始化2D渲染上下文
 */
function initVisualContext() {
    console.log('🎨 初始化可视化渲染上下文');
    
    const canvas = document.getElementById('canvas_video');
    if (!canvas) {
        console.error('❌ 找不到canvas_video元素');
        return false;
    }
    
    const ctx = canvas.getContext('2d');
    if (!ctx) {
        console.error('❌ 无法获取2D上下文');
        return false;
    }
    
    // 设置canvas样式
    canvas.style.background = 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)';
    
    window.VisualDigitalHuman.canvas = canvas;
    window.VisualDigitalHuman.ctx = ctx;
    
    console.log('✅ 可视化上下文初始化成功');
    return true;
}

/**
 * 创建简化的人脸模型
 */
function createFaceModel() {
    const canvas = window.VisualDigitalHuman.canvas;
    const centerX = canvas.width / 2;
    const centerY = canvas.height / 2;
    
    return {
        // 头部轮廓
        head: {
            x: centerX,
            y: centerY - 20,
            width: 160,
            height: 200
        },
        
        // 眼睛
        leftEye: {
            x: centerX - 40,
            y: centerY - 40,
            width: 25,
            height: 15
        },
        rightEye: {
            x: centerX + 15,
            y: centerY - 40,
            width: 25,
            height: 15
        },
        
        // 鼻子
        nose: {
            x: centerX - 5,
            y: centerY - 10,
            width: 10,
            height: 20
        },
        
        // 嘴巴 - 这是我们要动画的部分
        mouth: {
            x: centerX,
            y: centerY + 30,
            baseWidth: 40,
            baseHeight: 8,
            currentWidth: 40,
            currentHeight: 8,
            openness: 0 // 0-1，表示张开程度
        }
    };
}

/**
 * 根据嘴唇形状数组更新嘴巴参数
 */
function updateMouthShape(lipShapeArray) {
    if (!window.VisualDigitalHuman.faceModel || !lipShapeArray) return;
    
    const mouth = window.VisualDigitalHuman.faceModel.mouth;
    
    // 计算总的嘴唇活动强度
    const intensity = Math.max(...lipShapeArray.slice(0, 6));
    
    // 根据不同的音素调整嘴型
    const a_shape = lipShapeArray[0] || 0; // "啊" - 张大嘴
    const e_shape = lipShapeArray[1] || 0; // "诶" - 横向拉伸
    const i_shape = lipShapeArray[2] || 0; // "伊" - 嘴角上扬
    const o_shape = lipShapeArray[3] || 0; // "哦" - 圆形
    const u_shape = lipShapeArray[4] || 0; // "乌" - 嘟嘴
    
    // 计算嘴巴的宽度和高度
    mouth.currentWidth = mouth.baseWidth + (a_shape * 20) + (e_shape * 30) - (u_shape * 15);
    mouth.currentHeight = mouth.baseHeight + (a_shape * 25) + (o_shape * 15) + (u_shape * 10);
    mouth.openness = intensity;
    
    // 限制范围
    mouth.currentWidth = Math.max(20, Math.min(80, mouth.currentWidth));
    mouth.currentHeight = Math.max(5, Math.min(40, mouth.currentHeight));
}

/**
 * 绘制人脸
 */
function drawFace() {
    const ctx = window.VisualDigitalHuman.ctx;
    const canvas = window.VisualDigitalHuman.canvas;
    const face = window.VisualDigitalHuman.faceModel;
    
    if (!ctx || !face) return;
    
    // 清除画布
    ctx.clearRect(0, 0, canvas.width, canvas.height);
    
    // 绘制背景渐变
    const gradient = ctx.createLinearGradient(0, 0, canvas.width, canvas.height);
    gradient.addColorStop(0, '#667eea');
    gradient.addColorStop(1, '#764ba2');
    ctx.fillStyle = gradient;
    ctx.fillRect(0, 0, canvas.width, canvas.height);
    
    // 绘制头部轮廓
    ctx.fillStyle = '#f4c2a1'; // 肤色
    ctx.strokeStyle = '#d4a574';
    ctx.lineWidth = 2;
    ctx.beginPath();
    ctx.ellipse(face.head.x, face.head.y, face.head.width/2, face.head.height/2, 0, 0, Math.PI * 2);
    ctx.fill();
    ctx.stroke();
    
    // 绘制眼睛
    ctx.fillStyle = '#ffffff';
    ctx.strokeStyle = '#333333';
    ctx.lineWidth = 1;
    
    // 左眼
    ctx.beginPath();
    ctx.ellipse(face.leftEye.x, face.leftEye.y, face.leftEye.width/2, face.leftEye.height/2, 0, 0, Math.PI * 2);
    ctx.fill();
    ctx.stroke();
    
    // 右眼
    ctx.beginPath();
    ctx.ellipse(face.rightEye.x, face.rightEye.y, face.rightEye.width/2, face.rightEye.height/2, 0, 0, Math.PI * 2);
    ctx.fill();
    ctx.stroke();
    
    // 绘制眼珠
    ctx.fillStyle = '#333333';
    ctx.beginPath();
    ctx.arc(face.leftEye.x, face.leftEye.y, 6, 0, Math.PI * 2);
    ctx.fill();
    
    ctx.beginPath();
    ctx.arc(face.rightEye.x, face.rightEye.y, 6, 0, Math.PI * 2);
    ctx.fill();
    
    // 绘制鼻子
    ctx.strokeStyle = '#d4a574';
    ctx.lineWidth = 2;
    ctx.beginPath();
    ctx.moveTo(face.nose.x, face.nose.y);
    ctx.lineTo(face.nose.x - 3, face.nose.y + face.nose.height);
    ctx.moveTo(face.nose.x, face.nose.y + face.nose.height);
    ctx.lineTo(face.nose.x + 3, face.nose.y + face.nose.height);
    ctx.stroke();
    
    // 绘制嘴巴 - 这是关键部分
    drawMouth(face.mouth);
}

/**
 * 绘制嘴巴
 */
function drawMouth(mouth) {
    const ctx = window.VisualDigitalHuman.ctx;
    
    // 嘴巴颜色根据张开程度变化
    const openness = mouth.openness;
    const lipColor = `hsl(${350 + openness * 20}, 70%, ${50 + openness * 20}%)`;
    
    ctx.fillStyle = lipColor;
    ctx.strokeStyle = '#b85450';
    ctx.lineWidth = 2;
    
    if (openness > 0.1) {
        // 张开的嘴巴 - 绘制椭圆
        ctx.beginPath();
        ctx.ellipse(mouth.x, mouth.y, mouth.currentWidth/2, mouth.currentHeight/2, 0, 0, Math.PI * 2);
        ctx.fill();
        ctx.stroke();
        
        // 绘制牙齿
        if (openness > 0.3) {
            ctx.fillStyle = '#ffffff';
            ctx.beginPath();
            ctx.ellipse(mouth.x, mouth.y - mouth.currentHeight/4, mouth.currentWidth/3, mouth.currentHeight/4, 0, 0, Math.PI * 2);
            ctx.fill();
        }
    } else {
        // 闭合的嘴巴 - 绘制线条
        ctx.beginPath();
        ctx.moveTo(mouth.x - mouth.currentWidth/2, mouth.y);
        ctx.quadraticCurveTo(mouth.x, mouth.y + 3, mouth.x + mouth.currentWidth/2, mouth.y);
        ctx.stroke();
    }
}

/**
 * 绘制嘴唇同步状态信息
 */
function drawLipSyncInfo(lipShapeArray) {
    const ctx = window.VisualDigitalHuman.ctx;
    const canvas = window.VisualDigitalHuman.canvas;
    
    if (!lipShapeArray) return;
    
    // 绘制音素信息
    ctx.fillStyle = 'rgba(0, 0, 0, 0.7)';
    ctx.fillRect(10, 10, 200, 120);
    
    ctx.fillStyle = '#ffffff';
    ctx.font = '14px monospace';
    ctx.fillText('🎤 嘴唇同步状态', 20, 30);
    
    const phonemes = ['A(啊)', 'E(诶)', 'I(伊)', 'O(哦)', 'U(乌)', 'M(嗯)'];
    
    for (let i = 0; i < 6; i++) {
        const value = lipShapeArray[i] || 0;
        const barWidth = value * 100;
        const y = 50 + i * 15;
        
        // 绘制标签
        ctx.fillStyle = '#ffffff';
        ctx.fillText(phonemes[i], 20, y);
        
        // 绘制进度条背景
        ctx.fillStyle = 'rgba(255, 255, 255, 0.2)';
        ctx.fillRect(80, y - 10, 100, 12);
        
        // 绘制进度条
        ctx.fillStyle = `hsl(${120 - value * 60}, 70%, 50%)`;
        ctx.fillRect(80, y - 10, barWidth, 12);
        
        // 绘制数值
        ctx.fillStyle = '#ffffff';
        ctx.fillText(value.toFixed(2), 190, y);
    }
}

/**
 * 渲染一帧
 */
function renderVisualFrame(lipShapeArray) {
    if (!window.VisualDigitalHuman.isInitialized) return;
    
    // 更新嘴巴形状
    updateMouthShape(lipShapeArray);
    
    // 绘制人脸
    drawFace();
    
    // 绘制状态信息
    drawLipSyncInfo(lipShapeArray);
    
    // 存储当前嘴唇形状
    window.VisualDigitalHuman.currentLipShape = lipShapeArray;
}

/**
 * 启动渲染循环
 */
function startVisualRenderLoop() {
    function render() {
        if (!window.VisualDigitalHuman.isInitialized) return;
        
        // 获取当前嘴唇形状
        let lipShapeArray = null;
        if (window.customLipSync && window.customLipSync.isActive) {
            lipShapeArray = window.customLipSync.getLipShapeArray();
        } else {
            // 默认的静态形状
            lipShapeArray = new Array(6).fill(0);
        }
        
        // 渲染帧
        renderVisualFrame(lipShapeArray);
        
        // 继续下一帧
        window.VisualDigitalHuman.animationId = requestAnimationFrame(render);
    }
    
    render();
    console.log('🎬 可视化渲染循环已启动');
}

/**
 * 停止渲染循环
 */
function stopVisualRenderLoop() {
    if (window.VisualDigitalHuman.animationId) {
        cancelAnimationFrame(window.VisualDigitalHuman.animationId);
        window.VisualDigitalHuman.animationId = null;
        console.log('⏹️ 可视化渲染循环已停止');
    }
}

/**
 * 初始化可视化数字人系统
 */
function initVisualDigitalHuman() {
    console.log('🚀 初始化可视化数字人系统');
    
    // 初始化渲染上下文
    const contextSuccess = initVisualContext();
    if (!contextSuccess) {
        console.error('❌ 可视化上下文初始化失败');
        return false;
    }
    
    // 创建人脸模型
    window.VisualDigitalHuman.faceModel = createFaceModel();
    
    // 启动渲染循环
    startVisualRenderLoop();
    
    window.VisualDigitalHuman.isInitialized = true;
    console.log('✅ 可视化数字人系统初始化完成');
    
    return true;
}

// 导出控制接口
window.visualDigitalHuman = {
    init: initVisualDigitalHuman,
    render: renderVisualFrame,
    start: startVisualRenderLoop,
    stop: stopVisualRenderLoop,
    isReady: () => window.VisualDigitalHuman.isInitialized
};

console.log('💡 可视化数字人系统控制命令:');
console.log('  visualDigitalHuman.init() - 初始化系统');
console.log('  visualDigitalHuman.start() - 启动渲染');
console.log('  visualDigitalHuman.stop() - 停止渲染');
console.log('  visualDigitalHuman.isReady() - 检查状态');
