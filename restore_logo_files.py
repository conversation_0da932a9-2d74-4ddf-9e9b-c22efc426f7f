#!/usr/bin/env python3
"""
恢复combined_data.json.gz文件到原始状态（显示logo）
"""

import os
import gzip
import json
import shutil
from pathlib import Path

def restore_combined_data_file(file_path):
    """
    恢复单个combined_data.json.gz文件，将authorized字段设为False
    
    Args:
        file_path (str): combined_data.json.gz文件的路径
    
    Returns:
        bool: 恢复是否成功
    """
    try:
        # 检查是否有备份文件
        backup_path = file_path + ".backup"
        if os.path.exists(backup_path):
            print(f"发现备份文件: {backup_path}")
            choice = input("是否使用备份文件恢复？(y/N): ").lower().strip()
            if choice in ['y', 'yes']:
                shutil.copy2(backup_path, file_path)
                print(f"已从备份恢复: {file_path}")
                return True
        
        # 手动修改文件
        print(f"手动修改文件: {file_path}")
        
        # 创建新的备份
        new_backup_path = file_path + ".modified_backup"
        shutil.copy2(file_path, new_backup_path)
        print(f"已创建修改前备份: {new_backup_path}")
        
        # 读取原文件
        with gzip.open(file_path, 'rt', encoding='UTF-8') as f:
            combined_data = json.load(f)
        
        # 检查当前authorized状态
        current_status = combined_data.get("authorized", False)
        print(f"当前authorized状态: {current_status}")
        
        if not current_status:
            print("文件已经是未授权状态（显示logo）")
            os.remove(new_backup_path)  # 删除不必要的备份
            return True
        
        # 修改authorized字段
        combined_data["authorized"] = False
        
        # 写回文件
        with gzip.open(file_path, 'wt', encoding='UTF-8') as f:
            json.dump(combined_data, f)
        
        print(f"成功恢复文件: {file_path}")
        print("authorized字段已设置为False，logo将显示")
        
        # 验证修改结果
        with gzip.open(file_path, 'rt', encoding='UTF-8') as f:
            verify_data = json.load(f)
        
        if not verify_data.get("authorized", True):
            print("恢复验证成功")
            os.remove(new_backup_path)  # 删除备份文件
            return True
        else:
            print("恢复验证失败，恢复备份文件")
            shutil.move(new_backup_path, file_path)
            return False
            
    except Exception as e:
        print(f"恢复文件时出错: {e}")
        # 如果有备份文件，尝试恢复
        new_backup_path = file_path + ".modified_backup"
        if os.path.exists(new_backup_path):
            shutil.move(new_backup_path, file_path)
            print("已恢复备份文件")
        return False

def find_combined_data_files(root_dir="."):
    """
    查找所有的combined_data.json.gz文件
    
    Args:
        root_dir (str): 搜索的根目录
    
    Returns:
        list: 找到的文件路径列表
    """
    combined_data_files = []
    
    for root, dirs, files in os.walk(root_dir):
        for file in files:
            if file == "combined_data.json.gz":
                file_path = os.path.join(root, file)
                combined_data_files.append(file_path)
    
    return combined_data_files

def main():
    """主函数"""
    print("=== 数字人形象Logo恢复工具 ===")
    print("此工具将恢复logo显示（设置authorized=false）")
    print("正在搜索combined_data.json.gz文件...")
    
    # 查找所有combined_data.json.gz文件
    files = find_combined_data_files()
    
    if not files:
        print("未找到任何combined_data.json.gz文件")
        return
    
    print(f"找到 {len(files)} 个文件:")
    for i, file_path in enumerate(files, 1):
        print(f"  {i}. {file_path}")
    
    # 询问用户确认
    print("\n⚠️  警告: 此操作将恢复logo显示")
    print("是否要恢复所有这些文件的logo显示？")
    choice = input("输入 'y' 或 'yes' 确认，其他任意键取消: ").lower().strip()
    
    if choice not in ['y', 'yes']:
        print("操作已取消")
        return
    
    # 批量处理文件
    success_count = 0
    total_count = len(files)
    
    print(f"\n开始处理 {total_count} 个文件...")
    
    for i, file_path in enumerate(files, 1):
        print(f"\n[{i}/{total_count}] 处理文件: {file_path}")
        
        if restore_combined_data_file(file_path):
            success_count += 1
        else:
            print(f"处理失败: {file_path}")
    
    # 输出结果
    print(f"\n=== 处理完成 ===")
    print(f"总文件数: {total_count}")
    print(f"成功处理: {success_count}")
    print(f"失败数量: {total_count - success_count}")
    
    if success_count == total_count:
        print("所有文件都已成功恢复logo显示！")
    elif success_count > 0:
        print("部分文件处理成功，请检查失败的文件")
    else:
        print("所有文件处理失败，请检查错误信息")

if __name__ == "__main__":
    main()
