/**
 * 简化的数字人渲染系统
 * 专注于嘴唇同步，移除复杂的视频处理
 */

console.log('🎭 简化数字人渲染系统已加载');

window.SimpleDigitalHuman = {
    isInitialized: false,
    canvas: null,
    gl: null,
    combinedData: null,
    objData: null,
    currentFrame: 0
};

/**
 * 初始化WebGL上下文
 */
function initWebGLContext() {
    console.log('🎨 初始化WebGL上下文');
    
    const canvas = document.getElementById('canvas_video');
    if (!canvas) {
        console.error('❌ 找不到canvas_video元素');
        return false;
    }
    
    const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
    if (!gl) {
        console.error('❌ 无法获取WebGL上下文');
        return false;
    }
    
    // 设置视口
    gl.viewport(0, 0, canvas.width, canvas.height);
    gl.clearColor(0.2, 0.2, 0.2, 1.0);
    gl.enable(gl.DEPTH_TEST);
    
    window.SimpleDigitalHuman.canvas = canvas;
    window.SimpleDigitalHuman.gl = gl;
    
    console.log('✅ WebGL上下文初始化成功');
    return true;
}

/**
 * 加载数字人数据
 */
async function loadDigitalHumanData() {
    console.log('📊 加载数字人数据');
    
    try {
        const response = await fetch('assets/combined_data.json.gz');
        const compressedData = await response.arrayBuffer();
        const decompressedData = pako.inflate(new Uint8Array(compressedData), { to: 'string' });
        const combinedData = JSON.parse(decompressedData);
        
        window.SimpleDigitalHuman.combinedData = combinedData;
        
        // 解析OBJ数据
        if (combinedData.face3D_obj) {
            window.SimpleDigitalHuman.objData = parseObjData(combinedData.face3D_obj.join('\n'));
        }
        
        console.log('✅ 数字人数据加载成功');
        console.log('📊 数据统计:', {
            jsonSets: combinedData.json_data ? combinedData.json_data.length : 0,
            vertices: window.SimpleDigitalHuman.objData ? window.SimpleDigitalHuman.objData.vertices.length : 0,
            faces: window.SimpleDigitalHuman.objData ? window.SimpleDigitalHuman.objData.faces.length : 0
        });
        
        return true;
    } catch (error) {
        console.error('❌ 数字人数据加载失败:', error);
        return false;
    }
}

/**
 * 简化的OBJ数据解析
 */
function parseObjData(objString) {
    const vertices = [];
    const faces = [];
    const lines = objString.split('\n');
    
    for (const line of lines) {
        const parts = line.trim().split(/\s+/);
        
        if (parts[0] === 'v') {
            // 顶点数据
            vertices.push({
                x: parseFloat(parts[1]),
                y: parseFloat(parts[2]),
                z: parseFloat(parts[3])
            });
        } else if (parts[0] === 'f') {
            // 面数据
            const face = [];
            for (let i = 1; i < parts.length; i++) {
                const vertexIndex = parseInt(parts[i].split('/')[0]) - 1;
                face.push(vertexIndex);
            }
            faces.push(face);
        }
    }
    
    return { vertices, faces };
}

/**
 * 创建着色器
 */
function createShader(gl, type, source) {
    const shader = gl.createShader(type);
    gl.shaderSource(shader, source);
    gl.compileShader(shader);
    
    if (!gl.getShaderParameter(shader, gl.COMPILE_STATUS)) {
        console.error('着色器编译失败:', gl.getShaderInfoLog(shader));
        gl.deleteShader(shader);
        return null;
    }
    
    return shader;
}

/**
 * 创建着色器程序
 */
function createShaderProgram(gl) {
    const vertexShaderSource = `
        attribute vec3 a_position;
        attribute vec3 a_normal;
        
        uniform mat4 u_modelViewMatrix;
        uniform mat4 u_projectionMatrix;
        uniform vec3 u_lightDirection;
        
        varying vec3 v_normal;
        varying vec3 v_lighting;
        
        void main() {
            gl_Position = u_projectionMatrix * u_modelViewMatrix * vec4(a_position, 1.0);
            
            v_normal = a_normal;
            
            // 简单的光照计算
            vec3 ambientLight = vec3(0.3, 0.3, 0.3);
            vec3 directionalLightColor = vec3(1.0, 1.0, 1.0);
            vec3 directionalVector = normalize(u_lightDirection);
            
            float directional = max(dot(v_normal, directionalVector), 0.0);
            v_lighting = ambientLight + (directionalLightColor * directional);
        }
    `;
    
    const fragmentShaderSource = `
        precision mediump float;
        
        varying vec3 v_normal;
        varying vec3 v_lighting;
        
        void main() {
            vec3 baseColor = vec3(0.8, 0.7, 0.6); // 肤色
            gl_FragColor = vec4(baseColor * v_lighting, 1.0);
        }
    `;
    
    const vertexShader = createShader(gl, gl.VERTEX_SHADER, vertexShaderSource);
    const fragmentShader = createShader(gl, gl.FRAGMENT_SHADER, fragmentShaderSource);
    
    if (!vertexShader || !fragmentShader) {
        return null;
    }
    
    const program = gl.createProgram();
    gl.attachShader(program, vertexShader);
    gl.attachShader(program, fragmentShader);
    gl.linkProgram(program);
    
    if (!gl.getProgramParameter(program, gl.LINK_STATUS)) {
        console.error('着色器程序链接失败:', gl.getProgramInfoLog(program));
        return null;
    }
    
    return program;
}

/**
 * 简化的渲染函数
 */
function renderFrame(lipShapeArray) {
    const gl = window.SimpleDigitalHuman.gl;
    if (!gl) return;
    
    // 清除画布
    gl.clear(gl.COLOR_BUFFER_BIT | gl.DEPTH_BUFFER_BIT);
    
    // 这里可以添加基本的3D渲染逻辑
    // 暂时只显示一个简单的背景
    
    // 如果有自定义嘴唇数据，在这里应用
    if (lipShapeArray && lipShapeArray.length > 0) {
        // 应用嘴唇形状变形
        applyLipShapeDeformation(lipShapeArray);
    }
    
    // 绘制一个简单的指示器显示嘴唇同步状态
    drawLipSyncIndicator(lipShapeArray);
}

/**
 * 应用嘴唇形状变形
 */
function applyLipShapeDeformation(lipShapeArray) {
    // 这里可以实现基于lipShapeArray的顶点变形
    // 暂时只是记录数据
    if (Math.random() < 0.01) { // 1%概率输出调试信息
        console.log('🎭 应用嘴唇变形:', lipShapeArray.slice(0, 6).map(v => v.toFixed(3)));
    }
}

/**
 * 绘制嘴唇同步指示器
 */
function drawLipSyncIndicator(lipShapeArray) {
    const gl = window.SimpleDigitalHuman.gl;
    const canvas = window.SimpleDigitalHuman.canvas;
    
    if (!gl || !canvas) return;
    
    // 计算嘴唇活动强度
    let intensity = 0;
    if (lipShapeArray && lipShapeArray.length > 0) {
        intensity = Math.max(...lipShapeArray.slice(0, 6));
    }
    
    // 绘制一个简单的圆形指示器
    const centerX = canvas.width / 2;
    const centerY = canvas.height / 2;
    const radius = 50 + intensity * 30;
    
    // 使用2D上下文绘制指示器
    const ctx = canvas.getContext('2d');
    if (ctx) {
        ctx.save();
        ctx.globalAlpha = 0.7;
        ctx.fillStyle = `hsl(${120 - intensity * 60}, 70%, 50%)`;
        ctx.beginPath();
        ctx.arc(centerX, centerY, radius, 0, Math.PI * 2);
        ctx.fill();
        ctx.restore();
    }
}

/**
 * 初始化简化数字人系统
 */
async function initSimpleDigitalHuman() {
    console.log('🚀 初始化简化数字人系统');
    
    // 等待WASM模块
    if (window.wasmHelper) {
        await window.wasmHelper.waitForWasm();
    }
    
    // 初始化WebGL
    const webglSuccess = initWebGLContext();
    if (!webglSuccess) {
        console.error('❌ WebGL初始化失败');
        return false;
    }
    
    // 加载数据
    const dataSuccess = await loadDigitalHumanData();
    if (!dataSuccess) {
        console.error('❌ 数据加载失败');
        return false;
    }
    
    // 启动渲染循环
    startRenderLoop();
    
    window.SimpleDigitalHuman.isInitialized = true;
    console.log('✅ 简化数字人系统初始化完成');
    
    return true;
}

/**
 * 启动渲染循环
 */
function startRenderLoop() {
    function render() {
        if (!window.SimpleDigitalHuman.isInitialized) return;
        
        // 获取当前嘴唇形状
        let lipShapeArray = null;
        if (window.customLipSync && window.customLipSync.isActive) {
            lipShapeArray = window.customLipSync.getLipShapeArray();
        }
        
        // 渲染帧
        renderFrame(lipShapeArray);
        
        // 继续下一帧
        requestAnimationFrame(render);
    }
    
    render();
    console.log('🎬 渲染循环已启动');
}

// 导出控制接口
window.simpleDigitalHuman = {
    init: initSimpleDigitalHuman,
    render: renderFrame,
    isReady: () => window.SimpleDigitalHuman.isInitialized
};

console.log('💡 简化数字人系统控制命令:');
console.log('  simpleDigitalHuman.init() - 初始化系统');
console.log('  simpleDigitalHuman.render() - 手动渲染一帧');
console.log('  simpleDigitalHuman.isReady() - 检查是否准备就绪');
