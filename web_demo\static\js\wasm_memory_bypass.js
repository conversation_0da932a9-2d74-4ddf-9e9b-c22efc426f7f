/**
 * 基于WASM内存直接修改的绕过方案
 * 根据example.wat分析，数据段从地址1024开始，包含authorized字符串
 */

console.log('🔧 WASM内存绕过脚本已加载');

window.WasmMemoryBypass = {
    isActive: false,
    authorizedOffset: null,
    originalValue: null
};

/**
 * 在WASM内存中搜索authorized字符串
 */
function findAuthorizedString() {
    if (!Module || !Module.HEAPU8) {
        console.log('❌ WASM内存未就绪');
        return null;
    }
    
    const searchString = 'authorized';
    const searchBytes = new TextEncoder().encode(searchString);
    
    // 从数据段开始搜索 (地址1024开始)
    const startAddr = 1024;
    const endAddr = Math.min(startAddr + 100000, Module.HEAPU8.length); // 搜索100KB范围
    
    console.log(`🔍 在内存地址 ${startAddr} - ${endAddr} 搜索 "${searchString}"`);
    
    for (let addr = startAddr; addr < endAddr - searchBytes.length; addr++) {
        let found = true;
        for (let i = 0; i < searchBytes.length; i++) {
            if (Module.HEAPU8[addr + i] !== searchBytes[i]) {
                found = false;
                break;
            }
        }
        
        if (found) {
            console.log(`✅ 在地址 ${addr} 找到 "${searchString}"`);
            return addr;
        }
    }
    
    console.log(`❌ 未找到 "${searchString}"`);
    return null;
}

/**
 * 搜索JSON数据中的authorized字段
 */
function findAuthorizedInJson() {
    if (!Module || !Module.HEAPU8) {
        return null;
    }
    
    // 搜索JSON模式: "authorized":false 或 "authorized": false
    const patterns = [
        '"authorized":false',
        '"authorized": false',
        '"authorized":true',
        '"authorized": true'
    ];
    
    for (const pattern of patterns) {
        const searchBytes = new TextEncoder().encode(pattern);
        const startAddr = 1024;
        const endAddr = Math.min(startAddr + 200000, Module.HEAPU8.length);
        
        for (let addr = startAddr; addr < endAddr - searchBytes.length; addr++) {
            let found = true;
            for (let i = 0; i < searchBytes.length; i++) {
                if (Module.HEAPU8[addr + i] !== searchBytes[i]) {
                    found = false;
                    break;
                }
            }
            
            if (found) {
                console.log(`✅ 在地址 ${addr} 找到JSON模式: "${pattern}"`);
                return { addr, pattern, length: searchBytes.length };
            }
        }
    }
    
    return null;
}

/**
 * 直接修改WASM内存中的authorized值
 */
function patchAuthorizedInMemory() {
    console.log('🔧 开始修改WASM内存中的authorized值');
    
    // 方法1: 搜索JSON中的authorized字段
    const jsonMatch = findAuthorizedInJson();
    if (jsonMatch) {
        console.log(`📝 修改JSON中的authorized值`);
        
        // 将false替换为true
        if (jsonMatch.pattern.includes('false')) {
            const trueBytes = new TextEncoder().encode('"authorized":true');
            const falseBytes = new TextEncoder().encode('"authorized":false');
            
            if (jsonMatch.pattern === '"authorized":false') {
                // 直接替换
                for (let i = 0; i < trueBytes.length && i < falseBytes.length; i++) {
                    Module.HEAPU8[jsonMatch.addr + i] = trueBytes[i];
                }
                console.log('✅ 已将 "authorized":false 修改为 "authorized":true');
                return true;
            }
        }
    }
    
    // 方法2: 搜索字符串并修改周围的值
    const authorizedAddr = findAuthorizedString();
    if (authorizedAddr) {
        console.log(`📝 在authorized字符串周围搜索布尔值`);
        
        // 在authorized字符串前后搜索false/true
        const searchRange = 50;
        for (let offset = -searchRange; offset <= searchRange; offset++) {
            const addr = authorizedAddr + offset;
            if (addr >= 0 && addr < Module.HEAPU8.length - 5) {
                // 检查是否是false (5字节)
                const falseBytes = new TextEncoder().encode('false');
                let isMatch = true;
                for (let i = 0; i < falseBytes.length; i++) {
                    if (Module.HEAPU8[addr + i] !== falseBytes[i]) {
                        isMatch = false;
                        break;
                    }
                }
                
                if (isMatch) {
                    console.log(`✅ 在地址 ${addr} 找到false，修改为true`);
                    const trueBytes = new TextEncoder().encode('true\0'); // 添加null终止符
                    for (let i = 0; i < trueBytes.length; i++) {
                        Module.HEAPU8[addr + i] = trueBytes[i];
                    }
                    return true;
                }
            }
        }
    }
    
    console.log('❌ 未能找到可修改的authorized值');
    return false;
}

/**
 * 监控内存变化并实时修改
 */
function startMemoryMonitoring() {
    console.log('👁️ 开始监控内存变化');
    
    let lastCheck = 0;
    const checkInterval = 1000; // 每秒检查一次
    
    const monitor = setInterval(() => {
        const now = Date.now();
        if (now - lastCheck < checkInterval) return;
        lastCheck = now;
        
        // 检查并修改内存中的authorized值
        const success = patchAuthorizedInMemory();
        if (success) {
            console.log('🔄 内存中的authorized值已更新');
        }
    }, 100);
    
    // 5分钟后停止监控
    setTimeout(() => {
        clearInterval(monitor);
        console.log('⏰ 内存监控已停止');
    }, 300000);
    
    return monitor;
}

/**
 * 安装内存绕过
 */
async function installMemoryBypass() {
    console.log('🚀 安装WASM内存绕过');

    // 使用WASM助手等待模块准备就绪
    if (window.wasmHelper) {
        await window.wasmHelper.waitForWasm();
    } else {
        console.log('⚠️ WASM助手未加载，使用备用等待方法');
        await new Promise((resolve) => {
            const checkModule = () => {
                if (typeof Module !== 'undefined' && Module.HEAPU8 && Module._updateBlendShape) {
                    resolve(true);
                } else {
                    setTimeout(checkModule, 500);
                }
            };
            checkModule();
        });
    }
    
    console.log(`📊 WASM内存大小: ${Module.HEAPU8.length} 字节`);
    
    // 立即尝试修改
    const success = patchAuthorizedInMemory();
    if (success) {
        console.log('✅ 初始内存修改成功');
    }
    
    // 开始监控
    const monitor = startMemoryMonitoring();
    
    window.WasmMemoryBypass.isActive = true;
    window.WasmMemoryBypass.monitor = monitor;
    
    console.log('✅ WASM内存绕过已安装');
    
    // 添加控制函数
    window.testMemoryBypass = {
        patch: patchAuthorizedInMemory,
        find: findAuthorizedString,
        findJson: findAuthorizedInJson,
        status: () => {
            console.log('📊 内存绕过状态:', {
                active: window.WasmMemoryBypass.isActive,
                memorySize: Module.HEAPU8 ? Module.HEAPU8.length : 0,
                hasModule: typeof Module !== 'undefined'
            });
        }
    };
    
    console.log('💡 可以在控制台使用以下命令:');
    console.log('  testMemoryBypass.patch() - 手动修改内存');
    console.log('  testMemoryBypass.find() - 搜索authorized字符串');
    console.log('  testMemoryBypass.findJson() - 搜索JSON中的authorized');
    console.log('  testMemoryBypass.status() - 查看状态');
}

// 等待页面加载完成
window.addEventListener('load', function() {
    console.log('📋 页面加载完成，准备安装内存绕过');

    // 立即尝试安装，内部会等待WASM模块
    installMemoryBypass();
});

// 如果WASM模块已经准备就绪
if (typeof Module !== 'undefined' && Module.ready) {
    Module.ready.then(() => {
        console.log('🎉 WASM模块ready事件触发');
        installMemoryBypass();
    });
}

console.log('💡 WASM内存绕过系统已加载，等待WASM模块就绪...');
