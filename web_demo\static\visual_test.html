<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>可视化数字人测试</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            font-family: 'Arial', sans-serif;
            color: white;
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            text-align: center;
        }
        
        h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .subtitle {
            font-size: 1.2em;
            margin-bottom: 30px;
            opacity: 0.9;
        }
        
        .video-container {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 15px;
            padding: 20px;
            margin: 20px auto;
            display: inline-block;
            box-shadow: 0 8px 32px rgba(0,0,0,0.3);
        }
        
        #canvas_video {
            border: 2px solid rgba(255, 255, 255, 0.2);
            border-radius: 10px;
            background: #000;
            max-width: 100%;
            height: auto;
        }
        
        .controls {
            margin-top: 20px;
            display: flex;
            justify-content: center;
            gap: 15px;
            flex-wrap: wrap;
        }
        
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 25px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }
        
        .btn-primary {
            background: linear-gradient(45deg, #28a745, #20c997);
            color: white;
        }
        
        .btn-danger {
            background: linear-gradient(45deg, #dc3545, #fd7e14);
            color: white;
        }
        
        .btn-info {
            background: linear-gradient(45deg, #17a2b8, #6f42c1);
            color: white;
        }
        
        .btn-warning {
            background: linear-gradient(45deg, #ffc107, #fd7e14);
            color: white;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0,0,0,0.3);
        }
        
        .status-panel {
            background: rgba(0, 0, 0, 0.4);
            border-radius: 10px;
            padding: 15px;
            margin: 20px auto;
            max-width: 600px;
            text-align: left;
        }
        
        .status-item {
            display: flex;
            justify-content: space-between;
            margin: 8px 0;
            padding: 5px 0;
            border-bottom: 1px solid rgba(255,255,255,0.1);
        }
        
        .status-label {
            font-weight: bold;
        }
        
        .status-value {
            font-family: monospace;
        }
        
        .demo-controls {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 20px;
            margin: 20px auto;
            max-width: 800px;
        }
        
        .demo-controls h3 {
            margin-top: 0;
            color: #ffd700;
        }
        
        .slider-group {
            margin: 15px 0;
            text-align: left;
        }
        
        .slider-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        
        .slider {
            width: 100%;
            height: 8px;
            border-radius: 5px;
            background: rgba(255, 255, 255, 0.3);
            outline: none;
            -webkit-appearance: none;
        }
        
        .slider::-webkit-slider-thumb {
            -webkit-appearance: none;
            appearance: none;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: #4CAF50;
            cursor: pointer;
        }
        
        .slider::-moz-range-thumb {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: #4CAF50;
            cursor: pointer;
            border: none;
        }
        
        @media (max-width: 768px) {
            .controls {
                flex-direction: column;
                align-items: center;
            }
            
            .btn {
                width: 200px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>👁️ 可视化数字人测试</h1>
        <p class="subtitle">实时嘴唇同步动画演示 - 无需WASM</p>
        
        <div class="video-container">
            <canvas id="canvas_video" width="512" height="512"></canvas>
        </div>
        
        <div class="controls">
            <button class="btn btn-primary" onclick="startLipSync()">
                🎤 启动嘴唇同步
            </button>
            <button class="btn btn-danger" onclick="stopLipSync()">
                ⏹️ 停止同步
            </button>
            <button class="btn btn-info" onclick="testAnimation()">
                🎭 测试动画
            </button>
            <button class="btn btn-warning" onclick="resetFace()">
                🔄 重置人脸
            </button>
        </div>
        
        <div class="status-panel">
            <h3 style="margin-top: 0; color: #ffd700;">📊 系统状态</h3>
            <div class="status-item">
                <span class="status-label">可视化系统:</span>
                <span class="status-value" id="visual-status">未初始化</span>
            </div>
            <div class="status-item">
                <span class="status-label">嘴唇同步:</span>
                <span class="status-value" id="lip-status">未启动</span>
            </div>
            <div class="status-item">
                <span class="status-label">音频权限:</span>
                <span class="status-value" id="audio-status">未请求</span>
            </div>
            <div class="status-item">
                <span class="status-label">渲染状态:</span>
                <span class="status-value" id="render-status">停止</span>
            </div>
        </div>
        
        <div class="demo-controls">
            <h3>🎛️ 手动控制嘴型</h3>
            <div class="slider-group">
                <label for="a-slider">A (啊) - 张大嘴:</label>
                <input type="range" id="a-slider" class="slider" min="0" max="1" step="0.01" value="0">
                <span id="a-value">0.00</span>
            </div>
            <div class="slider-group">
                <label for="e-slider">E (诶) - 横向拉伸:</label>
                <input type="range" id="e-slider" class="slider" min="0" max="1" step="0.01" value="0">
                <span id="e-value">0.00</span>
            </div>
            <div class="slider-group">
                <label for="i-slider">I (伊) - 嘴角上扬:</label>
                <input type="range" id="i-slider" class="slider" min="0" max="1" step="0.01" value="0">
                <span id="i-value">0.00</span>
            </div>
            <div class="slider-group">
                <label for="o-slider">O (哦) - 圆形:</label>
                <input type="range" id="o-slider" class="slider" min="0" max="1" step="0.01" value="0">
                <span id="o-value">0.00</span>
            </div>
            <div class="slider-group">
                <label for="u-slider">U (乌) - 嘟嘴:</label>
                <input type="range" id="u-slider" class="slider" min="0" max="1" step="0.01" value="0">
                <span id="u-value">0.00</span>
            </div>
        </div>
    </div>

    <!-- 加载脚本 -->
    <script src="js/custom_lip_sync.js"></script>
    <script src="js/visual_digital_human.js"></script>

    <script>
        let isManualMode = false;
        
        // 更新状态显示
        function updateStatus(elementId, text, color = '#ccc') {
            const element = document.getElementById(elementId);
            if (element) {
                element.textContent = text;
                element.style.color = color;
            }
        }
        
        // 启动嘴唇同步
        async function startLipSync() {
            console.log('🚀 启动嘴唇同步');
            
            try {
                // 初始化可视化系统
                if (window.visualDigitalHuman && !window.visualDigitalHuman.isReady()) {
                    const success = window.visualDigitalHuman.init();
                    if (success) {
                        updateStatus('visual-status', '✅ 已初始化', '#28a745');
                        updateStatus('render-status', '✅ 运行中', '#28a745');
                    }
                }
                
                // 启动音频嘴唇同步
                if (window.customLipSync) {
                    const success = await window.customLipSync.start();
                    if (success) {
                        updateStatus('lip-status', '✅ 运行中', '#28a745');
                        updateStatus('audio-status', '✅ 已授权', '#28a745');
                        isManualMode = false;
                        alert('✅ 嘴唇同步已启动！请对着麦克风说话。');
                    } else {
                        alert('❌ 嘴唇同步启动失败，请检查麦克风权限。');
                    }
                } else {
                    alert('❌ 嘴唇同步系统未加载');
                }
            } catch (error) {
                console.error('启动失败:', error);
                alert('❌ 启动失败: ' + error.message);
            }
        }
        
        // 停止嘴唇同步
        function stopLipSync() {
            console.log('⏹️ 停止嘴唇同步');
            
            if (window.customLipSync) {
                window.customLipSync.stop();
                updateStatus('lip-status', '⏹️ 已停止', '#ffc107');
                isManualMode = true;
            }
        }
        
        // 测试动画
        function testAnimation() {
            console.log('🎭 测试动画');
            
            if (!window.visualDigitalHuman || !window.visualDigitalHuman.isReady()) {
                window.visualDigitalHuman.init();
                updateStatus('visual-status', '✅ 已初始化', '#28a745');
                updateStatus('render-status', '✅ 运行中', '#28a745');
            }
            
            isManualMode = true;
            
            // 播放一个测试动画序列
            const testSequence = [
                [0.8, 0, 0, 0, 0, 0], // A
                [0, 0.8, 0, 0, 0, 0], // E
                [0, 0, 0.8, 0, 0, 0], // I
                [0, 0, 0, 0.8, 0, 0], // O
                [0, 0, 0, 0, 0.8, 0], // U
                [0, 0, 0, 0, 0, 0]    // 静音
            ];
            
            let index = 0;
            const interval = setInterval(() => {
                if (index >= testSequence.length) {
                    clearInterval(interval);
                    return;
                }
                
                const shape = testSequence[index];
                if (window.visualDigitalHuman) {
                    window.visualDigitalHuman.render(shape);
                }
                
                index++;
            }, 500);
        }
        
        // 重置人脸
        function resetFace() {
            console.log('🔄 重置人脸');
            
            // 重置所有滑块
            document.getElementById('a-slider').value = 0;
            document.getElementById('e-slider').value = 0;
            document.getElementById('i-slider').value = 0;
            document.getElementById('o-slider').value = 0;
            document.getElementById('u-slider').value = 0;
            
            updateSliderValues();
            
            if (window.visualDigitalHuman) {
                window.visualDigitalHuman.render([0, 0, 0, 0, 0, 0]);
            }
        }
        
        // 更新滑块值显示
        function updateSliderValues() {
            const sliders = ['a', 'e', 'i', 'o', 'u'];
            sliders.forEach(slider => {
                const value = document.getElementById(slider + '-slider').value;
                document.getElementById(slider + '-value').textContent = parseFloat(value).toFixed(2);
            });
        }
        
        // 从滑块获取嘴型数据
        function getLipShapeFromSliders() {
            return [
                parseFloat(document.getElementById('a-slider').value),
                parseFloat(document.getElementById('e-slider').value),
                parseFloat(document.getElementById('i-slider').value),
                parseFloat(document.getElementById('o-slider').value),
                parseFloat(document.getElementById('u-slider').value),
                0 // M音素暂时不用
            ];
        }
        
        // 绑定滑块事件
        window.addEventListener('load', () => {
            const sliders = ['a', 'e', 'i', 'o', 'u'];
            sliders.forEach(slider => {
                const element = document.getElementById(slider + '-slider');
                element.addEventListener('input', () => {
                    updateSliderValues();
                    
                    if (isManualMode && window.visualDigitalHuman) {
                        const lipShape = getLipShapeFromSliders();
                        window.visualDigitalHuman.render(lipShape);
                    }
                });
            });
            
            // 初始化显示
            updateSliderValues();
            
            // 自动初始化可视化系统
            setTimeout(() => {
                if (window.visualDigitalHuman) {
                    const success = window.visualDigitalHuman.init();
                    if (success) {
                        updateStatus('visual-status', '✅ 已初始化', '#28a745');
                        updateStatus('render-status', '✅ 运行中', '#28a745');
                        isManualMode = true;
                    }
                }
            }, 1000);
        });
        
        // 定期更新状态
        setInterval(() => {
            if (window.visualDigitalHuman && window.visualDigitalHuman.isReady()) {
                updateStatus('visual-status', '✅ 运行中', '#28a745');
                updateStatus('render-status', '✅ 运行中', '#28a745');
            }
            
            if (window.customLipSync && window.customLipSync.isActive) {
                updateStatus('lip-status', '✅ 运行中', '#28a745');
            }
        }, 1000);
    </script>
</body>
</html>
