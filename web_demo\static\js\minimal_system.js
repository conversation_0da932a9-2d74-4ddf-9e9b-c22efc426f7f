/**
 * 精简版数字人系统
 * 移除原有嘴唇控制，只保留必要的渲染功能
 */

console.log('🎯 精简版数字人系统已加载');

window.MinimalSystem = {
    isInitialized: false,
    canvas: null,
    gl: null,
    program: null,
    customLipSync: null
};

/**
 * 初始化WebGL渲染系统
 */
function initWebGL() {
    console.log('🎨 初始化WebGL渲染系统');
    
    // 获取canvas
    const canvas = document.getElementById('canvas_video');
    if (!canvas) {
        console.error('❌ 找不到canvas_video元素');
        return false;
    }
    
    // 获取WebGL上下文
    const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
    if (!gl) {
        console.error('❌ 无法获取WebGL上下文');
        return false;
    }
    
    window.MinimalSystem.canvas = canvas;
    window.MinimalSystem.gl = gl;
    
    console.log('✅ WebGL初始化成功');
    return true;
}

/**
 * 精简版渲染函数
 */
function minimalRender(imageData, lipShapeArray) {
    const gl = window.MinimalSystem.gl;
    if (!gl) return;
    
    try {
        // 清除画布
        gl.clearColor(0.0, 0.0, 0.0, 1.0);
        gl.clear(gl.COLOR_BUFFER_BIT);
        
        // 这里可以添加基本的图像渲染逻辑
        // 暂时只是一个占位符
        
        // 如果有自定义嘴唇同步数据，在这里应用
        if (window.customLipSync && window.customLipSync.isActive) {
            const customShape = window.customLipSync.getLipShapeArray();
            // 应用自定义嘴唇数据到渲染
            console.log('🎤 应用自定义嘴唇数据:', customShape.slice(0, 6));
        }
        
    } catch (error) {
        console.error('❌ 渲染失败:', error);
    }
}

/**
 * 替换原有的复杂渲染系统
 */
function replaceOriginalSystem() {
    console.log('🔄 替换原有渲染系统');
    
    // 如果存在原有的渲染函数，替换它们
    if (typeof render === 'function') {
        window.originalRender = render;
        window.render = function(mat_world, subPoints, bsArray) {
            console.log('🎯 使用精简版渲染');
            
            // 如果启用了自定义嘴唇同步，使用自定义数据
            if (window.customLipSync && window.customLipSync.isActive) {
                const customShape = window.customLipSync.getLipShapeArray();
                // 替换bsArray的前6个元素（嘴唇参数）
                for (let i = 0; i < 6 && i < bsArray.length; i++) {
                    bsArray[i] = customShape[i];
                }
            }
            
            // 调用原始渲染函数
            return window.originalRender(mat_world, subPoints, bsArray);
        };
        console.log('✅ 渲染函数已替换');
    }
    
    // 替换processDataSet函数
    if (typeof processDataSet === 'function') {
        window.originalProcessDataSet = processDataSet;
        window.processDataSet = function(currentDataSetIndex) {
            console.log('🎯 使用精简版数据处理');
            
            // 调用原始函数，但拦截嘴唇数据
            return window.originalProcessDataSet(currentDataSetIndex);
        };
        console.log('✅ 数据处理函数已替换');
    }
}

/**
 * 禁用原有的嘴唇控制
 */
async function disableOriginalLipControl() {
    console.log('🚫 禁用原有嘴唇控制');

    // 使用WASM助手等待模块准备就绪
    if (window.wasmHelper) {
        await window.wasmHelper.waitForWasm();
    } else {
        console.log('⚠️ WASM助手未加载，使用备用等待方法');
        await new Promise((resolve) => {
            const checkWasm = () => {
                if (typeof Module !== 'undefined' && Module._updateBlendShape && Module.HEAPU8) {
                    resolve(true);
                } else {
                    setTimeout(checkWasm, 500);
                }
            };
            checkWasm();
        });
    }

    // WASM模块已加载，替换关键函数
    if (typeof Module !== 'undefined' && Module._updateBlendShape) {
        window.originalUpdateBlendShape = Module._updateBlendShape;
        
        Module._updateBlendShape = function(bsPtr, size) {
            // 如果启用了自定义嘴唇同步
            if (window.customLipSync && window.customLipSync.isActive) {
                const customShape = window.customLipSync.getLipShapeArray();
                const bsArray = new Float32Array(Module.HEAPU8.buffer, bsPtr, 12);
                
                // 只更新嘴唇相关的参数（前6个）
                for (let i = 0; i < 6; i++) {
                    bsArray[i] = customShape[i];
                }
                
                console.log('🎤 使用自定义嘴唇数据');
                return; // 不调用原始函数
            } else {
                // 如果没有自定义嘴唇同步，调用原始函数
                return window.originalUpdateBlendShape(bsPtr, size);
            }
        };
        
        console.log('✅ WASM嘴唇控制已替换');
    }
}

/**
 * 创建简化的控制界面
 */
function createSimplifiedUI() {
    // 检查是否已存在
    if (document.getElementById('minimalSystemUI')) {
        return;
    }
    
    const ui = document.createElement('div');
    ui.id = 'minimalSystemUI';
    ui.style.cssText = `
        position: fixed;
        top: 10px;
        left: 10px;
        background: rgba(0, 0, 0, 0.8);
        color: white;
        padding: 15px;
        border-radius: 10px;
        font-family: monospace;
        font-size: 12px;
        z-index: 10000;
        min-width: 200px;
    `;
    
    ui.innerHTML = `
        <h3 style="margin: 0 0 10px 0; color: #ff6b6b;">🎯 精简系统</h3>
        <div style="margin-bottom: 10px;">
            <button id="enableCustomLip" style="margin-right: 5px; padding: 5px 10px; background: #28a745; color: white; border: none; border-radius: 3px; cursor: pointer;">启用自定义嘴唇</button>
            <button id="disableCustomLip" style="padding: 5px 10px; background: #dc3545; color: white; border: none; border-radius: 3px; cursor: pointer;">禁用</button>
        </div>
        <div id="systemStatus" style="font-size: 11px; color: #ccc;">
            状态: 精简系统运行中
        </div>
        <div style="margin-top: 10px; font-size: 10px; color: #888;">
            <div>WebGL: <span id="webglStatus">检测中...</span></div>
            <div>WASM: <span id="wasmStatus">检测中...</span></div>
            <div>自定义嘴唇: <span id="lipStatus">未启用</span></div>
        </div>
    `;
    
    document.body.appendChild(ui);
    
    // 绑定事件
    document.getElementById('enableCustomLip').onclick = async () => {
        if (window.customLipSync) {
            const success = await window.customLipSync.start();
            if (success) {
                document.getElementById('lipStatus').textContent = '已启用';
                document.getElementById('lipStatus').style.color = '#28a745';
            }
        }
    };
    
    document.getElementById('disableCustomLip').onclick = () => {
        if (window.customLipSync) {
            window.customLipSync.stop();
            document.getElementById('lipStatus').textContent = '已禁用';
            document.getElementById('lipStatus').style.color = '#dc3545';
        }
    };
    
    // 定期更新状态
    setInterval(() => {
        document.getElementById('webglStatus').textContent = window.MinimalSystem.gl ? '✅' : '❌';
        document.getElementById('wasmStatus').textContent = (typeof Module !== 'undefined' && Module._updateBlendShape) ? '✅' : '❌';
    }, 1000);
}

/**
 * 初始化精简系统
 */
async function initMinimalSystem() {
    console.log('🚀 初始化精简数字人系统');

    // 初始化WebGL
    const webglSuccess = initWebGL();
    if (!webglSuccess) {
        console.error('❌ WebGL初始化失败');
        return false;
    }

    // 替换原有系统
    replaceOriginalSystem();

    // 禁用原有嘴唇控制（异步等待WASM）
    await disableOriginalLipControl();

    // 创建UI
    createSimplifiedUI();

    window.MinimalSystem.isInitialized = true;
    console.log('✅ 精简系统初始化完成');

    return true;
}

/**
 * 恢复原有系统
 */
function restoreOriginalSystem() {
    console.log('🔄 恢复原有系统');
    
    if (window.originalRender) {
        window.render = window.originalRender;
    }
    
    if (window.originalProcessDataSet) {
        window.processDataSet = window.originalProcessDataSet;
    }
    
    if (window.originalUpdateBlendShape) {
        Module._updateBlendShape = window.originalUpdateBlendShape;
    }
    
    console.log('✅ 原有系统已恢复');
}

// 导出控制接口
window.minimalSystem = {
    init: initMinimalSystem,
    restore: restoreOriginalSystem,
    status: () => ({
        initialized: window.MinimalSystem.isInitialized,
        webgl: !!window.MinimalSystem.gl,
        wasm: typeof Module !== 'undefined' && !!Module._updateBlendShape,
        customLip: window.customLipSync ? window.customLipSync.isActive : false
    })
};

// 页面加载完成后自动初始化
window.addEventListener('load', function() {
    console.log('📋 页面加载完成，准备初始化精简系统');

    // 延迟初始化，确保其他脚本都已加载
    setTimeout(async () => {
        await initMinimalSystem();

        console.log('💡 精简系统控制命令:');
        console.log('  minimalSystem.init() - 初始化精简系统');
        console.log('  minimalSystem.restore() - 恢复原有系统');
        console.log('  minimalSystem.status() - 查看系统状态');
    }, 2000);
});

console.log('💡 精简版数字人系统已加载，等待初始化...');
