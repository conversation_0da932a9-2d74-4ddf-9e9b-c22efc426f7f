<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WASM加载测试</title>
    <style>
        body {
            font-family: monospace;
            padding: 20px;
            background: #1a1a1a;
            color: #00ff00;
        }
        .log {
            background: #000;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .success { background: #004400; }
        .error { background: #440000; }
        .warning { background: #444400; }
        .info { background: #000044; }
    </style>
</head>
<body>
    <h1>🔧 WASM模块加载诊断</h1>
    
    <div id="status" class="status info">
        📋 开始诊断...
    </div>
    
    <div class="log" id="log">
        等待日志输出...
    </div>
    
    <button onclick="runDiagnostics()" style="padding: 10px 20px; font-size: 16px;">
        🔍 运行完整诊断
    </button>
    
    <button onclick="clearLog()" style="padding: 10px 20px; font-size: 16px;">
        🗑️ 清除日志
    </button>

    <script>
        let logElement = document.getElementById('log');
        let statusElement = document.getElementById('status');
        
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logMessage = `[${timestamp}] ${message}\n`;
            logElement.textContent += logMessage;
            logElement.scrollTop = logElement.scrollHeight;
            console.log(message);
        }
        
        function updateStatus(message, type = 'info') {
            statusElement.textContent = message;
            statusElement.className = `status ${type}`;
        }
        
        function clearLog() {
            logElement.textContent = '';
        }
        
        // 配置Module对象 - 这是Qt WebAssembly应用
        var Module = {
            locateFile: function(path, scriptDirectory) {
                log(`🔍 locateFile调用: path=${path}, scriptDirectory=${scriptDirectory}`);
                if (path.endsWith('.wasm')) {
                    const wasmPath = 'DHLiveMini.wasm';
                    log(`📁 WASM文件路径: ${wasmPath}`);
                    return wasmPath;
                }
                return scriptDirectory + path;
            },

            onRuntimeInitialized: function() {
                log('🎉 WASM运行时初始化完成！');
                updateStatus('✅ WASM运行时已初始化', 'success');
                checkWasmFunctions();
            },

            print: function(text) {
                log(`📝 WASM输出: ${text}`);
            },

            printErr: function(text) {
                log(`❌ WASM错误: ${text}`, 'error');
            }
        };

        // Qt WebAssembly应用初始化函数
        async function initQtApp() {
            log('🚀 初始化Qt WebAssembly应用...');

            try {
                if (typeof createQtAppInstance === 'function') {
                    log('✅ 找到createQtAppInstance函数');

                    // 创建Qt应用实例
                    const qtApp = await createQtAppInstance(Module);
                    log('✅ Qt应用实例创建成功');

                    // 更新全局Module对象
                    if (qtApp) {
                        window.Module = qtApp;
                        log('✅ 全局Module对象已更新');

                        // 等待ready Promise
                        if (qtApp.ready) {
                            await qtApp.ready;
                            log('✅ Qt应用ready Promise已解决');
                        }

                        checkWasmFunctions();
                    }
                } else {
                    log('❌ 未找到createQtAppInstance函数');
                }
            } catch (error) {
                log(`❌ Qt应用初始化失败: ${error.message}`);
            }
        }
        
        function checkWasmFunctions() {
            log('🔍 检查WASM函数可用性...');
            
            const functions = [
                '_updateBlendShape',
                '_processImage', 
                '_processJson',
                '_malloc',
                '_free'
            ];
            
            let allAvailable = true;
            
            functions.forEach(funcName => {
                if (typeof Module[funcName] === 'function') {
                    log(`✅ ${funcName}: 可用`);
                } else {
                    log(`❌ ${funcName}: 不可用`);
                    allAvailable = false;
                }
            });
            
            if (Module.HEAPU8) {
                log(`✅ HEAPU8内存: ${Module.HEAPU8.length} 字节`);
            } else {
                log(`❌ HEAPU8内存: 不可用`);
                allAvailable = false;
            }
            
            if (allAvailable) {
                updateStatus('✅ 所有WASM功能都可用', 'success');
                log('🎉 WASM模块完全准备就绪！');
            } else {
                updateStatus('⚠️ 部分WASM功能不可用', 'warning');
            }
        }
        
        function checkFileExists(url) {
            return fetch(url, { method: 'HEAD' })
                .then(response => {
                    if (response.ok) {
                        log(`✅ 文件存在: ${url}`);
                        return true;
                    } else {
                        log(`❌ 文件不存在: ${url} (状态: ${response.status})`);
                        return false;
                    }
                })
                .catch(error => {
                    log(`❌ 检查文件失败: ${url} - ${error.message}`);
                    return false;
                });
        }
        
        async function runDiagnostics() {
            log('🚀 开始完整诊断...');
            updateStatus('🔍 正在运行诊断...', 'info');

            // 1. 检查必要文件
            log('\n📁 检查必要文件...');
            const files = [
                'DHLiveMini.wasm',
                'js/DHLiveMini.js',
                'js/pako.min.js',
                'js/mp4box.all.min.js',
                'assets/combined_data.json.gz'
            ];

            for (const file of files) {
                await checkFileExists(file);
            }

            // 2. 检查createQtAppInstance函数
            log('\n🔍 检查Qt应用函数...');
            if (typeof createQtAppInstance === 'function') {
                log('✅ createQtAppInstance函数已定义');

                // 尝试初始化Qt应用
                await initQtApp();
            } else {
                log('❌ createQtAppInstance函数未定义');
            }

            // 3. 检查Module对象
            log('\n🔍 检查Module对象...');
            if (typeof Module !== 'undefined') {
                log('✅ Module对象已定义');
                log(`📊 Module属性: ${Object.keys(Module).join(', ')}`);

                if (Module.ready && typeof Module.ready.then === 'function') {
                    log('⏳ 等待Module.ready Promise...');
                    try {
                        await Module.ready;
                        log('✅ Module.ready Promise已解决');
                    } catch (error) {
                        log(`❌ Module.ready Promise失败: ${error.message}`);
                    }
                }
            } else {
                log('❌ Module对象未定义');
            }
            
            // 3. 检查WebAssembly支持
            log('\n🌐 检查WebAssembly支持...');
            if (typeof WebAssembly !== 'undefined') {
                log('✅ 浏览器支持WebAssembly');
                if (typeof WebAssembly.instantiateStreaming === 'function') {
                    log('✅ 支持WebAssembly.instantiateStreaming');
                } else {
                    log('⚠️ 不支持WebAssembly.instantiateStreaming');
                }
            } else {
                log('❌ 浏览器不支持WebAssembly');
            }
            
            // 4. 检查网络环境
            log('\n🌍 检查网络环境...');
            log(`📍 当前URL: ${window.location.href}`);
            log(`🔗 协议: ${window.location.protocol}`);
            log(`🏠 主机: ${window.location.host}`);
            
            // 5. 最终状态
            log('\n📊 诊断完成');
            if (typeof Module !== 'undefined' && Module._updateBlendShape) {
                updateStatus('✅ 诊断完成 - WASM模块正常', 'success');
            } else {
                updateStatus('⚠️ 诊断完成 - WASM模块有问题', 'warning');
            }
        }
        
        // 页面加载完成后自动开始诊断
        window.addEventListener('load', () => {
            log('📋 页面加载完成');
            setTimeout(() => {
                log('⏳ 等待3秒后开始自动诊断...');
                setTimeout(runDiagnostics, 3000);
            }, 1000);
        });
        
        // 监听错误
        window.addEventListener('error', (event) => {
            log(`❌ 全局错误: ${event.error ? event.error.message : event.message}`);
        });
        
        // 监听未处理的Promise拒绝
        window.addEventListener('unhandledrejection', (event) => {
            log(`❌ 未处理的Promise拒绝: ${event.reason}`);
        });
    </script>
    
    <!-- 加载必要的脚本 -->
    <script src="js/pako.min.js"></script>
    <script src="js/mp4box.all.min.js"></script>
    <script src="js/DHLiveMini.js"></script>
</body>
</html>
