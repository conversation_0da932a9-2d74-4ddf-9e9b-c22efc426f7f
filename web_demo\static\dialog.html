<!doctype html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI聊天</title>
    <link href="css/material-icons.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 0;
            display: flex;
            flex-direction: column;
            height: 100vh;
        }

        .top-area {
            height: 60%;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .chat-container {
            flex: 1;
            overflow-y: auto;
            padding: 20px;
            display: flex;
            flex-direction: column;
            background-image: linear-gradient(to bottom, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 0.6) 100%);
        }

        .message {
            margin-bottom: 15px;
            display: flex;
            align-items: flex-end;
        }

        .message.ai {
            justify-content: flex-start;
        }

        .message.user {
            justify-content: flex-end;
        }

        .message-content {
            max-width: 70%;
            padding: 10px 15px;
            border-radius: 15px;
            position: relative;
        }

        .message.ai .message-content {
            background-color: #e0e0e0;
            color: #333;
        }

        .message.user .message-content {
            background-color: #007bff;
            color: #fff;
        }

        .input-container {
            display: flex;
            align-items: center;
            padding: 10px;
            background-color: #fff;
            border-top: 1px solid #ddd;
        }

        .input-area {
            display: flex;
            align-items: center;
            background-color: #f0f0f0;
            border-radius: 25px;
            padding: 10px;
            height: 50px;
            box-sizing: border-box;
            width: 100%;
        }

        .input-area input {
            flex: 1;
            border: none;
            outline: none;
            font-size: 16px;
            padding: 5px;
            background: none;
        }

        .input-area button {
            background: none;
            border: none;
            font-size: 24px;
            color: #007bff;
            cursor: pointer;
            margin: 0 5px;
        }

        .input-area button:hover {
            color: #0056b3;
        }

        .voice-input-area {
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: #f0f0f0;
            border-radius: 25px;
            padding: 10px;
            height: 50px;
            box-sizing: border-box;
            width: 100%;
            cursor: pointer;
        }

        .voice-input-area span {
            font-size: 16px;
            color: #007bff;
        }
    </style>
</head>
<body>
    <!-- 上半区域 -->
    <div class="top-area"></div>

    <!-- 聊天容器 -->
    <div class="chat-container" id="chat-container">
        <!-- 聊天记录将显示在这里 -->
    </div>

    <!-- 输入区域 -->
    <div class="input-container">
        <div class="input-area" id="input-area">
            <button class="input-toggle" id="toggle-button">
                <i class="material-icons">keyboard</i>
            </button>
            <div class="voice-input-area" id="voice-input-area">
                <span>点击说话</span>
            </div>
            <input type="text" id="text-input" placeholder="发送消息" style="display: none;">
            <button class="send-button" id="send-button" style="display: none;">
                <i class="material-icons">send</i>
            </button>
        </div>
    </div>
    <script src="js/dialog.js"></script>
</body>
</html>