<!doctype html>
<html lang="en-us">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
        <meta name="viewport" content="width=device-width, height=device-height, user-scalable=0"/>
        <link rel="icon" href="common/favicon.ico" type="image/x-icon">
        <title>MiniLive</title>
        <style>
            @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

            * {
                box-sizing: border-box;
            }

            body {
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
                height: 100vh;
                margin: 0;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                background-attachment: fixed;
                overflow: hidden;
                font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                position: relative;
            }

            /* 科技感背景动画 */
            body::before {
                content: '';
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background:
                    radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
                    radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
                    radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.2) 0%, transparent 50%);
                animation: backgroundShift 20s ease-in-out infinite;
                z-index: -1;
            }

            @keyframes backgroundShift {
                0%, 100% { opacity: 1; }
                50% { opacity: 0.8; }
            }

            video, canvas {
                border: 2px solid rgba(255, 255, 255, 0.2);
                border-radius: 12px;
                box-shadow:
                    0 8px 32px rgba(0, 0, 0, 0.3),
                    0 0 0 1px rgba(255, 255, 255, 0.1),
                    inset 0 1px 0 rgba(255, 255, 255, 0.2);
                backdrop-filter: blur(10px);
                transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            }

            video:hover, canvas:hover {
                box-shadow:
                    0 12px 40px rgba(0, 0, 0, 0.4),
                    0 0 0 1px rgba(255, 255, 255, 0.2),
                    inset 0 1px 0 rgba(255, 255, 255, 0.3);
                transform: translateY(-2px);
            }

            #canvas_video {
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                object-fit: contain;
            }

            #canvas_gl {
                position: absolute;
                top: -9999px;
                left: -9999px;
                width: 128px;
                height: 128px;
            }

            #screen {
                position: absolute;
                bottom: -1000;
                right: -1000;
                width: 1px;
                height: 1px;
            }

            #screen2 {
                width: 100%;
                height: 100%;
                position: absolute;
                top: 0;
                left: 0;
                border: none;
                z-index: 5;
                border-radius: 12px;
                overflow: hidden;
            }

            /* 美化加载和开始消息 */
            #startMessage {
                position: absolute;
                top: 60%;
                left: 50%;
                transform: translate(-50%, -50%);
                font-size: 28px;
                font-weight: 600;
                color: #ffffff;
                z-index: -2;
                text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
                opacity: 0.9;
                animation: pulse 2s ease-in-out infinite;
            }

            @keyframes pulse {
                0%, 100% { opacity: 0.9; transform: translate(-50%, -50%) scale(1); }
                50% { opacity: 1; transform: translate(-50%, -50%) scale(1.02); }
            }
            #dropdownContainer, #voiceDropdownContainer {
                position: absolute;
                top: 24px;
                left: 24px;
                z-index: 10;
                opacity: 0;
                animation: slideInLeft 0.6s ease-out 0.5s forwards;
            }

            #voiceDropdownContainer {
                top: 84px;
                animation-delay: 0.7s;
            }

            @keyframes slideInLeft {
                from {
                    opacity: 0;
                    transform: translateX(-30px);
                }
                to {
                    opacity: 1;
                    transform: translateX(0);
                }
            }

            #characterDropdown, #voiceDropdown {
                appearance: none;
                -webkit-appearance: none;
                -moz-appearance: none;
                padding: 14px 50px 14px 20px;
                font-size: 15px;
                font-weight: 500;
                color: #ffffff;
                background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
                border: 1px solid rgba(255, 255, 255, 0.2);
                border-radius: 12px;
                box-shadow:
                    0 8px 32px rgba(0, 0, 0, 0.2),
                    inset 0 1px 0 rgba(255, 255, 255, 0.2);
                backdrop-filter: blur(20px);
                cursor: pointer;
                outline: none;
                transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                min-width: 140px;
                font-family: inherit;
            }

            #characterDropdown:hover, #voiceDropdown:hover {
                background: linear-gradient(135deg, rgba(255, 255, 255, 0.15) 0%, rgba(255, 255, 255, 0.08) 100%);
                border-color: rgba(255, 255, 255, 0.3);
                box-shadow:
                    0 12px 40px rgba(0, 0, 0, 0.3),
                    inset 0 1px 0 rgba(255, 255, 255, 0.3);
                transform: translateY(-2px);
            }

            #characterDropdown:focus, #voiceDropdown:focus {
                background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0.1) 100%);
                border-color: rgba(120, 219, 255, 0.6);
                box-shadow:
                    0 12px 40px rgba(0, 0, 0, 0.3),
                    0 0 0 3px rgba(120, 219, 255, 0.2),
                    inset 0 1px 0 rgba(255, 255, 255, 0.3);
                transform: translateY(-2px);
            }

            #characterDropdown option, #voiceDropdown option {
                background: rgba(30, 30, 50, 0.95);
                color: #ffffff;
                padding: 10px;
                border: none;
            }

            #dropdownContainer::after, #voiceDropdownContainer::after {
                content: '▼';
                position: absolute;
                top: 50%;
                right: 18px;
                transform: translateY(-50%);
                pointer-events: none;
                color: rgba(255, 255, 255, 0.7);
                font-size: 12px;
                transition: all 0.3s ease;
            }

            #dropdownContainer:hover::after, #voiceDropdownContainer:hover::after {
                color: rgba(255, 255, 255, 0.9);
                transform: translateY(-50%) scale(1.1);
            }
            #canvasEl {
                position: absolute;
                left: -9999px;
                top: -9999px;
                width: 300px;
                height: 150px;
            }

            /* 美化加载动画 */
            #loadingSpinner {
                position: relative;
                margin: 0;
                padding: 20px 30px;
                background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
                border: 1px solid rgba(255, 255, 255, 0.2);
                border-radius: 16px;
                box-shadow:
                    0 8px 32px rgba(0, 0, 0, 0.2),
                    inset 0 1px 0 rgba(255, 255, 255, 0.2);
                backdrop-filter: blur(20px);
                color: #ffffff;
                font-size: 18px;
                font-weight: 500;
                text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
                animation: loadingPulse 1.5s ease-in-out infinite;
                z-index: 100;
            }

            #loadingSpinner::before {
                content: '';
                position: absolute;
                top: -2px;
                left: -2px;
                right: -2px;
                bottom: -2px;
                background: linear-gradient(45deg, #667eea, #764ba2, #667eea);
                border-radius: 18px;
                z-index: -1;
                animation: borderGlow 2s linear infinite;
                opacity: 0.6;
            }

            @keyframes loadingPulse {
                0%, 100% {
                    transform: scale(1);
                    opacity: 0.9;
                }
                50% {
                    transform: scale(1.02);
                    opacity: 1;
                }
            }

            @keyframes borderGlow {
                0% { background-position: 0% 50%; }
                50% { background-position: 100% 50%; }
                100% { background-position: 0% 50%; }
            }

            /* 响应式设计 */
            @media (max-width: 768px) {
                #dropdownContainer, #voiceDropdownContainer {
                    left: 16px;
                    top: 16px;
                }

                #voiceDropdownContainer {
                    top: 70px;
                }

                #characterDropdown, #voiceDropdown {
                    padding: 12px 40px 12px 16px;
                    font-size: 14px;
                    min-width: 120px;
                }

                #startMessage {
                    font-size: 24px;
                }

                #loadingSpinner {
                    padding: 16px 24px;
                    font-size: 16px;
                }
            }

            @media (max-width: 480px) {
                #dropdownContainer, #voiceDropdownContainer {
                    left: 12px;
                    top: 12px;
                }

                #voiceDropdownContainer {
                    top: 60px;
                }

                #characterDropdown, #voiceDropdown {
                    padding: 10px 35px 10px 14px;
                    font-size: 13px;
                    min-width: 100px;
                }

                #startMessage {
                    font-size: 20px;
                }

                #loadingSpinner {
                    padding: 14px 20px;
                    font-size: 15px;
                }
            }
        </style>
    </head>
    <body>
        <div id="dropdownContainer">
            <select id="characterDropdown">
                <option value="assets">男性一</option>
                <option value="assets2">女性一</option>
            </select>
        </div>
        <div id="voiceDropdownContainer">
            <select id="voiceDropdown">
                <option value=0>温柔女</option>
                <option value=1>温柔男</option>
                <option value=2>甜美女</option>
                <option value=3>青年女</option>
                <option value=4>磁性男</option>
            </select>
        </div>

        <figure style="overflow:visible;" id="loadingSpinner">
            <strong>MiniMates: loading...</strong>
        </figure>
        <canvas id="canvasEl"></canvas>
        <canvas id="canvas_video"></canvas>
        <canvas id="canvas_gl" width="128" height="128"></canvas>
        <div id="screen"></div>
        <iframe id="screen2" src="dialog_RealTime.html" frameborder="0" style="display: none;"></iframe>
        <div id="startMessage">加载中</div>
        <script src="js/pako.min.js"></script>
        <script src="js/mp4box.all.min.js"></script>
        <script src="js/DHLiveMini.js"></script>
        <script src="js/MiniMateLoader.js"></script>
        <script src="js/MiniLive2.js"></script>
    </body>
</html>