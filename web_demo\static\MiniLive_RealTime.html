<!doctype html>
<html lang="en-us">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
        <meta name="viewport" content="width=device-width, height=device-height, user-scalable=0"/>
        <link rel="icon" href="common/favicon.ico" type="image/x-icon">
        <title>MiniLive</title>
        <style>
            @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

            * {
                box-sizing: border-box;
            }

            body {
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
                height: 100vh;
                margin: 0;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                background-attachment: fixed;
                overflow: hidden;
                font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                position: relative;
            }

            /* 增强科技感背景动画 */
            body::before {
                content: '';
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background:
                    radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.4) 0%, transparent 60%),
                    radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
                    radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.3) 0%, transparent 55%),
                    radial-gradient(circle at 60% 70%, rgba(147, 51, 234, 0.2) 0%, transparent 45%);
                animation: backgroundShift 25s ease-in-out infinite;
                z-index: -1;
            }

            /* 添加动态网格背景 */
            body::after {
                content: '';
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background-image:
                    linear-gradient(rgba(255, 255, 255, 0.03) 1px, transparent 1px),
                    linear-gradient(90deg, rgba(255, 255, 255, 0.03) 1px, transparent 1px);
                background-size: 50px 50px;
                animation: gridMove 30s linear infinite;
                z-index: -1;
                opacity: 0.6;
            }

            @keyframes backgroundShift {
                0%, 100% {
                    opacity: 1;
                    transform: scale(1) rotate(0deg);
                }
                33% {
                    opacity: 0.8;
                    transform: scale(1.05) rotate(1deg);
                }
                66% {
                    opacity: 0.9;
                    transform: scale(0.95) rotate(-1deg);
                }
            }

            @keyframes gridMove {
                0% { transform: translate(0, 0); }
                100% { transform: translate(50px, 50px); }
            }

            video, canvas {
                border: 2px solid rgba(255, 255, 255, 0.15);
                border-radius: 16px;
                box-shadow:
                    0 10px 40px rgba(0, 0, 0, 0.4),
                    0 0 0 1px rgba(255, 255, 255, 0.1),
                    inset 0 1px 0 rgba(255, 255, 255, 0.2),
                    0 0 20px rgba(120, 219, 255, 0.1);
                backdrop-filter: blur(15px);
                transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
                position: relative;
            }

            /* 主要画布样式 */
            #canvas_video {
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                object-fit: contain;
                z-index: 1;
            }

            video:hover, canvas:hover {
                box-shadow:
                    0 15px 50px rgba(0, 0, 0, 0.5),
                    0 0 0 1px rgba(255, 255, 255, 0.2),
                    inset 0 1px 0 rgba(255, 255, 255, 0.3),
                    0 0 30px rgba(120, 219, 255, 0.2);
                transform: translateY(-3px) scale(1.005);
                border-color: rgba(120, 219, 255, 0.4);
            }

            /* 添加容器包装器用于发光效果 */
            .canvas-container {
                position: relative;
                display: inline-block;
            }

            .canvas-container::before {
                content: '';
                position: absolute;
                top: -3px;
                left: -3px;
                right: -3px;
                bottom: -3px;
                background: linear-gradient(45deg,
                    rgba(120, 219, 255, 0.3),
                    rgba(147, 51, 234, 0.3),
                    rgba(255, 119, 198, 0.3),
                    rgba(120, 219, 255, 0.3));
                border-radius: 19px;
                z-index: -1;
                animation: borderRotate 4s linear infinite;
                opacity: 0;
                transition: opacity 0.3s ease;
                pointer-events: none;
            }

            .canvas-container:hover::before {
                opacity: 0.6;
            }

            @keyframes borderRotate {
                0% { transform: rotate(0deg); }
                100% { transform: rotate(360deg); }
            }

            #canvas_video {
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                object-fit: contain;
            }

            #canvas_gl {
                position: absolute;
                top: -9999px;
                left: -9999px;
                width: 128px;
                height: 128px;
            }

            #screen {
                position: absolute;
                bottom: -1000;
                right: -1000;
                width: 1px;
                height: 1px;
            }

            #screen2 {
                width: 100%;
                height: 100%;
                position: absolute;
                top: 0;
                left: 0;
                border: none;
                z-index: 5;
                border-radius: 12px;
                overflow: hidden;
            }

            /* 增强开始消息样式 */
            #startMessage {
                position: absolute;
                top: 60%;
                left: 50%;
                transform: translate(-50%, -50%);
                font-size: 32px;
                font-weight: 700;
                color: #ffffff;
                z-index: -2;
                text-shadow:
                    0 0 20px rgba(120, 219, 255, 0.6),
                    0 2px 15px rgba(0, 0, 0, 0.4),
                    0 0 40px rgba(120, 219, 255, 0.3);
                opacity: 0.95;
                animation: pulse 2.5s ease-in-out infinite;
                background: linear-gradient(45deg,
                    rgba(120, 219, 255, 0.1),
                    rgba(147, 51, 234, 0.1),
                    rgba(255, 119, 198, 0.1));
                padding: 16px 32px;
                border-radius: 16px;
                backdrop-filter: blur(15px);
                border: 1px solid rgba(255, 255, 255, 0.1);
                letter-spacing: 1px;
            }

            @keyframes pulse {
                0%, 100% {
                    opacity: 0.95;
                    transform: translate(-50%, -50%) scale(1);
                    text-shadow:
                        0 0 20px rgba(120, 219, 255, 0.6),
                        0 2px 15px rgba(0, 0, 0, 0.4),
                        0 0 40px rgba(120, 219, 255, 0.3);
                }
                50% {
                    opacity: 1;
                    transform: translate(-50%, -50%) scale(1.03);
                    text-shadow:
                        0 0 30px rgba(120, 219, 255, 0.8),
                        0 2px 20px rgba(0, 0, 0, 0.5),
                        0 0 60px rgba(120, 219, 255, 0.5);
                }
            }
            #dropdownContainer, #voiceDropdownContainer {
                position: absolute;
                top: 24px;
                left: 24px;
                z-index: 10;
                opacity: 0;
                animation: slideInLeft 0.8s cubic-bezier(0.4, 0, 0.2, 1) 0.5s forwards;
                filter: drop-shadow(0 8px 25px rgba(0, 0, 0, 0.15));
            }

            #voiceDropdownContainer {
                top: 88px;
                animation-delay: 0.8s;
            }

            @keyframes slideInLeft {
                from {
                    opacity: 0;
                    transform: translateX(-40px) scale(0.9);
                    filter: blur(5px);
                }
                to {
                    opacity: 1;
                    transform: translateX(0) scale(1);
                    filter: blur(0px);
                }
            }

            #characterDropdown, #voiceDropdown {
                appearance: none;
                -webkit-appearance: none;
                -moz-appearance: none;
                padding: 18px 60px 18px 24px;
                font-size: 15px;
                font-weight: 600;
                color: #ffffff;
                background: linear-gradient(135deg,
                    rgba(255, 255, 255, 0.15) 0%,
                    rgba(255, 255, 255, 0.08) 30%,
                    rgba(120, 219, 255, 0.12) 70%,
                    rgba(147, 51, 234, 0.1) 100%);
                border: 2px solid transparent;
                border-radius: 16px;
                box-shadow:
                    0 12px 40px rgba(0, 0, 0, 0.3),
                    inset 0 1px 0 rgba(255, 255, 255, 0.3),
                    0 0 0 1px rgba(120, 219, 255, 0.2),
                    0 4px 20px rgba(120, 219, 255, 0.1);
                backdrop-filter: blur(30px);
                cursor: pointer;
                outline: none;
                transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
                min-width: 160px;
                font-family: inherit;
                position: relative;
                overflow: hidden;
                letter-spacing: 0.5px;
                text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
            }

            /* 添加渐变边框效果 */
            #characterDropdown::after, #voiceDropdown::after {
                content: '';
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                border-radius: 16px;
                padding: 2px;
                background: linear-gradient(135deg,
                    rgba(120, 219, 255, 0.6),
                    rgba(147, 51, 234, 0.6),
                    rgba(255, 119, 198, 0.6),
                    rgba(120, 219, 255, 0.6));
                mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
                mask-composite: exclude;
                -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
                -webkit-mask-composite: xor;
                opacity: 0;
                transition: opacity 0.3s ease;
                pointer-events: none;
                z-index: -1;
            }

            /* 增强内部光效 */
            #characterDropdown::before, #voiceDropdown::before {
                content: '';
                position: absolute;
                top: 0;
                left: -100%;
                width: 100%;
                height: 100%;
                background: linear-gradient(90deg,
                    transparent,
                    rgba(255, 255, 255, 0.2),
                    rgba(120, 219, 255, 0.3),
                    rgba(255, 255, 255, 0.2),
                    transparent);
                transition: left 0.8s cubic-bezier(0.4, 0, 0.2, 1);
                pointer-events: none;
                z-index: 1;
            }

            #characterDropdown:hover, #voiceDropdown:hover {
                background: linear-gradient(135deg,
                    rgba(255, 255, 255, 0.22) 0%,
                    rgba(255, 255, 255, 0.14) 30%,
                    rgba(120, 219, 255, 0.18) 70%,
                    rgba(147, 51, 234, 0.15) 100%);
                box-shadow:
                    0 18px 50px rgba(0, 0, 0, 0.4),
                    inset 0 1px 0 rgba(255, 255, 255, 0.4),
                    0 0 0 1px rgba(120, 219, 255, 0.4),
                    0 6px 30px rgba(120, 219, 255, 0.25),
                    0 0 40px rgba(120, 219, 255, 0.15);
                transform: translateY(-4px) scale(1.03);
                filter: brightness(1.1);
            }

            #characterDropdown:hover::after, #voiceDropdown:hover::after {
                opacity: 1;
            }

            #characterDropdown:hover::before, #voiceDropdown:hover::before {
                left: 100%;
            }

            #characterDropdown:focus, #voiceDropdown:focus {
                background: linear-gradient(135deg,
                    rgba(255, 255, 255, 0.25) 0%,
                    rgba(255, 255, 255, 0.16) 30%,
                    rgba(120, 219, 255, 0.2) 70%,
                    rgba(147, 51, 234, 0.18) 100%);
                box-shadow:
                    0 20px 55px rgba(0, 0, 0, 0.45),
                    0 0 0 3px rgba(120, 219, 255, 0.4),
                    inset 0 1px 0 rgba(255, 255, 255, 0.5),
                    0 8px 35px rgba(120, 219, 255, 0.3),
                    0 0 50px rgba(120, 219, 255, 0.2);
                transform: translateY(-4px) scale(1.03);
                filter: brightness(1.15);
            }

            #characterDropdown:focus::after, #voiceDropdown:focus::after {
                opacity: 1;
            }

            #characterDropdown option, #voiceDropdown option {
                background: linear-gradient(135deg,
                    rgba(25, 25, 55, 0.98) 0%,
                    rgba(35, 35, 75, 0.96) 50%,
                    rgba(45, 45, 85, 0.95) 100%);
                color: #ffffff;
                padding: 14px 20px;
                border: none;
                font-weight: 500;
                transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                border-bottom: 1px solid rgba(255, 255, 255, 0.05);
                font-size: 14px;
                letter-spacing: 0.3px;
                text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
            }

            #characterDropdown option:first-child, #voiceDropdown option:first-child {
                border-top-left-radius: 12px;
                border-top-right-radius: 12px;
            }

            #characterDropdown option:last-child, #voiceDropdown option:last-child {
                border-bottom-left-radius: 12px;
                border-bottom-right-radius: 12px;
                border-bottom: none;
            }

            #characterDropdown option:hover, #voiceDropdown option:hover {
                background: linear-gradient(135deg,
                    rgba(120, 219, 255, 0.25) 0%,
                    rgba(147, 51, 234, 0.25) 50%,
                    rgba(255, 119, 198, 0.2) 100%);
                color: #ffffff;
                transform: translateX(4px);
                box-shadow:
                    inset 4px 0 0 rgba(120, 219, 255, 0.6),
                    0 2px 8px rgba(120, 219, 255, 0.2);
                text-shadow: 0 0 8px rgba(120, 219, 255, 0.4);
            }

            #characterDropdown option:checked, #voiceDropdown option:checked,
            #characterDropdown option:selected, #voiceDropdown option:selected {
                background: linear-gradient(135deg,
                    rgba(120, 219, 255, 0.3) 0%,
                    rgba(147, 51, 234, 0.3) 100%);
                color: #ffffff;
                font-weight: 600;
                box-shadow:
                    inset 4px 0 0 rgba(120, 219, 255, 0.8),
                    0 0 15px rgba(120, 219, 255, 0.3);
            }

            #dropdownContainer::after, #voiceDropdownContainer::after {
                content: '▼';
                position: absolute;
                top: 50%;
                right: 22px;
                transform: translateY(-50%);
                pointer-events: none;
                color: rgba(255, 255, 255, 0.9);
                font-size: 14px;
                transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
                text-shadow:
                    0 0 12px rgba(120, 219, 255, 0.4),
                    0 1px 3px rgba(0, 0, 0, 0.3);
                filter: drop-shadow(0 0 8px rgba(120, 219, 255, 0.3));
            }

            #dropdownContainer:hover::after, #voiceDropdownContainer:hover::after {
                color: rgba(120, 219, 255, 1);
                transform: translateY(-50%) scale(1.2) rotate(180deg);
                text-shadow:
                    0 0 20px rgba(120, 219, 255, 0.8),
                    0 0 30px rgba(120, 219, 255, 0.6),
                    0 2px 5px rgba(0, 0, 0, 0.4);
                filter: drop-shadow(0 0 15px rgba(120, 219, 255, 0.6));
                animation: arrowPulse 1s ease-in-out infinite;
            }

            @keyframes arrowPulse {
                0%, 100% {
                    transform: translateY(-50%) scale(1.2) rotate(180deg);
                }
                50% {
                    transform: translateY(-50%) scale(1.3) rotate(180deg);
                }
            }

            /* 增强标签效果 */
            #dropdownContainer::before {
                content: '🎭 角色选择';
                position: absolute;
                top: -12px;
                left: 16px;
                background: linear-gradient(135deg,
                    rgba(120, 219, 255, 0.95) 0%,
                    rgba(147, 51, 234, 0.95) 100%);
                color: #ffffff;
                padding: 4px 12px;
                border-radius: 8px;
                font-size: 12px;
                font-weight: 700;
                z-index: 15;
                opacity: 0;
                transform: translateY(-8px) scale(0.8);
                transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
                pointer-events: none;
                box-shadow:
                    0 4px 15px rgba(120, 219, 255, 0.3),
                    inset 0 1px 0 rgba(255, 255, 255, 0.3);
                backdrop-filter: blur(10px);
                border: 1px solid rgba(255, 255, 255, 0.2);
                letter-spacing: 0.5px;
                text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
            }

            #voiceDropdownContainer::before {
                content: '🎵 语音选择';
                position: absolute;
                top: -12px;
                left: 16px;
                background: linear-gradient(135deg,
                    rgba(255, 119, 198, 0.95) 0%,
                    rgba(120, 219, 255, 0.95) 100%);
                color: #ffffff;
                padding: 4px 12px;
                border-radius: 8px;
                font-size: 12px;
                font-weight: 700;
                z-index: 15;
                opacity: 0;
                transform: translateY(-8px) scale(0.8);
                transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
                pointer-events: none;
                box-shadow:
                    0 4px 15px rgba(255, 119, 198, 0.3),
                    inset 0 1px 0 rgba(255, 255, 255, 0.3);
                backdrop-filter: blur(10px);
                border: 1px solid rgba(255, 255, 255, 0.2);
                letter-spacing: 0.5px;
                text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
            }

            #dropdownContainer:hover::before, #voiceDropdownContainer:hover::before {
                opacity: 1;
                transform: translateY(0) scale(1);
            }

            /* 添加标签闪烁效果 */
            #dropdownContainer:focus-within::before {
                animation: labelGlow 2s ease-in-out infinite;
            }

            #voiceDropdownContainer:focus-within::before {
                animation: labelGlow 2s ease-in-out infinite;
            }

            @keyframes labelGlow {
                0%, 100% {
                    box-shadow:
                        0 4px 15px rgba(120, 219, 255, 0.3),
                        inset 0 1px 0 rgba(255, 255, 255, 0.3);
                }
                50% {
                    box-shadow:
                        0 6px 25px rgba(120, 219, 255, 0.6),
                        inset 0 1px 0 rgba(255, 255, 255, 0.5),
                        0 0 20px rgba(120, 219, 255, 0.4);
                }
            }
            #canvasEl {
                position: absolute;
                left: -9999px;
                top: -9999px;
                width: 300px;
                height: 150px;
            }

            /* 增强科技感加载动画 */
            #loadingSpinner {
                position: relative;
                margin: 0;
                padding: 24px 36px;
                background: linear-gradient(135deg,
                    rgba(255, 255, 255, 0.12) 0%,
                    rgba(255, 255, 255, 0.06) 50%,
                    rgba(120, 219, 255, 0.08) 100%);
                border: 1px solid rgba(255, 255, 255, 0.25);
                border-radius: 20px;
                box-shadow:
                    0 12px 40px rgba(0, 0, 0, 0.3),
                    inset 0 1px 0 rgba(255, 255, 255, 0.25),
                    0 0 0 1px rgba(120, 219, 255, 0.2);
                backdrop-filter: blur(25px);
                color: #ffffff;
                font-size: 19px;
                font-weight: 600;
                text-shadow: 0 2px 15px rgba(0, 0, 0, 0.4);
                animation: loadingPulse 2s ease-in-out infinite;
                z-index: 100;
                overflow: hidden;
            }

            #loadingSpinner::before {
                content: '';
                position: absolute;
                top: -3px;
                left: -3px;
                right: -3px;
                bottom: -3px;
                background: linear-gradient(45deg,
                    #667eea, #764ba2, #78dbff, #9333ea, #667eea);
                background-size: 300% 300%;
                border-radius: 23px;
                z-index: -1;
                animation: borderGlow 3s linear infinite;
                opacity: 0.8;
            }

            /* 添加内部扫描线效果 */
            #loadingSpinner::after {
                content: '';
                position: absolute;
                top: 0;
                left: -100%;
                width: 100%;
                height: 100%;
                background: linear-gradient(90deg,
                    transparent,
                    rgba(120, 219, 255, 0.3),
                    transparent);
                animation: scanLine 2s ease-in-out infinite;
                pointer-events: none;
            }

            @keyframes loadingPulse {
                0%, 100% {
                    transform: scale(1);
                    opacity: 0.95;
                }
                50% {
                    transform: scale(1.03);
                    opacity: 1;
                }
            }

            @keyframes borderGlow {
                0% { background-position: 0% 50%; }
                50% { background-position: 100% 50%; }
                100% { background-position: 0% 50%; }
            }

            @keyframes scanLine {
                0% { left: -100%; }
                50% { left: 100%; }
                100% { left: 100%; }
            }

            /* 增强响应式设计 */
            @media (max-width: 768px) {
                #dropdownContainer, #voiceDropdownContainer {
                    left: 16px;
                    top: 16px;
                    filter: drop-shadow(0 6px 20px rgba(0, 0, 0, 0.2));
                }

                #voiceDropdownContainer {
                    top: 76px;
                }

                #characterDropdown, #voiceDropdown {
                    padding: 14px 45px 14px 18px;
                    font-size: 14px;
                    min-width: 130px;
                    border-radius: 14px;
                }

                #dropdownContainer::after, #voiceDropdownContainer::after {
                    right: 18px;
                    font-size: 13px;
                }

                #dropdownContainer::before, #voiceDropdownContainer::before {
                    font-size: 11px;
                    padding: 3px 10px;
                    top: -10px;
                    left: 14px;
                }

                #startMessage {
                    font-size: 26px;
                    padding: 12px 24px;
                }

                #loadingSpinner {
                    padding: 18px 28px;
                    font-size: 17px;
                }
            }

            @media (max-width: 480px) {
                #dropdownContainer, #voiceDropdownContainer {
                    left: 12px;
                    top: 12px;
                    filter: drop-shadow(0 4px 15px rgba(0, 0, 0, 0.25));
                }

                #voiceDropdownContainer {
                    top: 68px;
                }

                #characterDropdown, #voiceDropdown {
                    padding: 12px 40px 12px 16px;
                    font-size: 13px;
                    min-width: 110px;
                    border-radius: 12px;
                    letter-spacing: 0.3px;
                }

                #dropdownContainer::after, #voiceDropdownContainer::after {
                    right: 16px;
                    font-size: 12px;
                }

                #dropdownContainer::before, #voiceDropdownContainer::before {
                    font-size: 10px;
                    padding: 2px 8px;
                    top: -8px;
                    left: 12px;
                }

                #startMessage {
                    font-size: 22px;
                    padding: 10px 20px;
                }

                #loadingSpinner {
                    padding: 16px 24px;
                    font-size: 15px;
                }
            }

            /* 超小屏幕优化 */
            @media (max-width: 360px) {
                #dropdownContainer, #voiceDropdownContainer {
                    left: 8px;
                    top: 8px;
                }

                #voiceDropdownContainer {
                    top: 60px;
                }

                #characterDropdown, #voiceDropdown {
                    padding: 10px 35px 10px 14px;
                    font-size: 12px;
                    min-width: 100px;
                    border-radius: 10px;
                }

                #dropdownContainer::before, #voiceDropdownContainer::before {
                    content: '🎭' !important;
                    font-size: 12px;
                    padding: 2px 6px;
                }

                #voiceDropdownContainer::before {
                    content: '🎵' !important;
                }
            }
        </style>
    </head>
    <body>
        <div id="dropdownContainer">
            <select id="characterDropdown">
                <option value="assets">男性一</option>
                <option value="assets2">女性一</option>
            </select>
        </div>
        <div id="voiceDropdownContainer">
            <select id="voiceDropdown">
                <option value=0>温柔女</option>
                <option value=1>温柔男</option>
                <option value=2>甜美女</option>
                <option value=3>青年女</option>
                <option value=4>磁性男</option>
            </select>
        </div>

        <figure style="overflow:visible;" id="loadingSpinner">
            <strong>MiniMates: loading...</strong>
        </figure>
        <canvas id="canvasEl"></canvas>
        <canvas id="canvas_video"></canvas>
        <canvas id="canvas_gl" width="128" height="128"></canvas>
        <div id="screen"></div>
        <iframe id="screen2" src="dialog_RealTime.html" frameborder="0" style="display: none;"></iframe>
        <div id="startMessage">加载中</div>
        <script src="js/pako.min.js"></script>
        <script src="js/mp4box.all.min.js"></script>
        <script src="js/DHLiveMini.js"></script>
        <script src="js/MiniMateLoader.js"></script>
        <script src="js/MiniLive2.js"></script>
    </body>
</html>