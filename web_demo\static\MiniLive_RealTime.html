<!doctype html>
<html lang="en-us">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
        <meta name="viewport" content="width=device-width, height=device-height, user-scalable=0"/>
        <link rel="icon" href="common/favicon.ico" type="image/x-icon">
        <title>MiniLive</title>
        <style>
            @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

            * {
                box-sizing: border-box;
            }

            body {
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
                height: 100vh;
                margin: 0;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                background-attachment: fixed;
                overflow: hidden;
                font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                position: relative;
            }

            /* 增强科技感背景动画 */
            body::before {
                content: '';
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background:
                    radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.4) 0%, transparent 60%),
                    radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
                    radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.3) 0%, transparent 55%),
                    radial-gradient(circle at 60% 70%, rgba(147, 51, 234, 0.2) 0%, transparent 45%);
                animation: backgroundShift 25s ease-in-out infinite;
                z-index: -1;
            }

            /* 添加动态网格背景 */
            body::after {
                content: '';
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background-image:
                    linear-gradient(rgba(255, 255, 255, 0.03) 1px, transparent 1px),
                    linear-gradient(90deg, rgba(255, 255, 255, 0.03) 1px, transparent 1px);
                background-size: 50px 50px;
                animation: gridMove 30s linear infinite;
                z-index: -1;
                opacity: 0.6;
            }

            @keyframes backgroundShift {
                0%, 100% {
                    opacity: 1;
                    transform: scale(1) rotate(0deg);
                }
                33% {
                    opacity: 0.8;
                    transform: scale(1.05) rotate(1deg);
                }
                66% {
                    opacity: 0.9;
                    transform: scale(0.95) rotate(-1deg);
                }
            }

            @keyframes gridMove {
                0% { transform: translate(0, 0); }
                100% { transform: translate(50px, 50px); }
            }

            video, canvas {
                border: 2px solid rgba(255, 255, 255, 0.15);
                border-radius: 16px;
                box-shadow:
                    0 10px 40px rgba(0, 0, 0, 0.4),
                    0 0 0 1px rgba(255, 255, 255, 0.1),
                    inset 0 1px 0 rgba(255, 255, 255, 0.2),
                    0 0 20px rgba(120, 219, 255, 0.1);
                backdrop-filter: blur(15px);
                transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
                position: relative;
            }

            /* 主要画布样式 */
            #canvas_video {
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                object-fit: contain;
                z-index: 1;
            }

            video:hover, canvas:hover {
                box-shadow:
                    0 15px 50px rgba(0, 0, 0, 0.5),
                    0 0 0 1px rgba(255, 255, 255, 0.2),
                    inset 0 1px 0 rgba(255, 255, 255, 0.3),
                    0 0 30px rgba(120, 219, 255, 0.2);
                transform: translateY(-3px) scale(1.005);
                border-color: rgba(120, 219, 255, 0.4);
            }

            /* 添加容器包装器用于发光效果 */
            .canvas-container {
                position: relative;
                display: inline-block;
            }

            .canvas-container::before {
                content: '';
                position: absolute;
                top: -3px;
                left: -3px;
                right: -3px;
                bottom: -3px;
                background: linear-gradient(45deg,
                    rgba(120, 219, 255, 0.3),
                    rgba(147, 51, 234, 0.3),
                    rgba(255, 119, 198, 0.3),
                    rgba(120, 219, 255, 0.3));
                border-radius: 19px;
                z-index: -1;
                animation: borderRotate 4s linear infinite;
                opacity: 0;
                transition: opacity 0.3s ease;
                pointer-events: none;
            }

            .canvas-container:hover::before {
                opacity: 0.6;
            }

            @keyframes borderRotate {
                0% { transform: rotate(0deg); }
                100% { transform: rotate(360deg); }
            }

            #canvas_video {
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                object-fit: contain;
            }

            #canvas_gl {
                position: absolute;
                top: -9999px;
                left: -9999px;
                width: 128px;
                height: 128px;
            }

            #screen {
                position: absolute;
                bottom: -1000;
                right: -1000;
                width: 1px;
                height: 1px;
            }

            #screen2 {
                width: 100%;
                height: 100%;
                position: absolute;
                top: 0;
                left: 0;
                border: none;
                z-index: 5;
                border-radius: 12px;
                overflow: hidden;
            }

            /* 增强开始消息样式 */
            #startMessage {
                position: absolute;
                top: 60%;
                left: 50%;
                transform: translate(-50%, -50%);
                font-size: 32px;
                font-weight: 700;
                color: #ffffff;
                z-index: -2;
                text-shadow:
                    0 0 20px rgba(120, 219, 255, 0.6),
                    0 2px 15px rgba(0, 0, 0, 0.4),
                    0 0 40px rgba(120, 219, 255, 0.3);
                opacity: 0.95;
                animation: pulse 2.5s ease-in-out infinite;
                background: linear-gradient(45deg,
                    rgba(120, 219, 255, 0.1),
                    rgba(147, 51, 234, 0.1),
                    rgba(255, 119, 198, 0.1));
                padding: 16px 32px;
                border-radius: 16px;
                backdrop-filter: blur(15px);
                border: 1px solid rgba(255, 255, 255, 0.1);
                letter-spacing: 1px;
            }

            @keyframes pulse {
                0%, 100% {
                    opacity: 0.95;
                    transform: translate(-50%, -50%) scale(1);
                    text-shadow:
                        0 0 20px rgba(120, 219, 255, 0.6),
                        0 2px 15px rgba(0, 0, 0, 0.4),
                        0 0 40px rgba(120, 219, 255, 0.3);
                }
                50% {
                    opacity: 1;
                    transform: translate(-50%, -50%) scale(1.03);
                    text-shadow:
                        0 0 30px rgba(120, 219, 255, 0.8),
                        0 2px 20px rgba(0, 0, 0, 0.5),
                        0 0 60px rgba(120, 219, 255, 0.5);
                }
            }
            #dropdownContainer, #voiceDropdownContainer {
                position: absolute;
                top: 24px;
                left: 24px;
                z-index: 10;
                opacity: 0;
                animation: slideInLeft 0.6s ease-out 0.5s forwards;
            }

            #voiceDropdownContainer {
                top: 84px;
                animation-delay: 0.7s;
            }

            @keyframes slideInLeft {
                from {
                    opacity: 0;
                    transform: translateX(-30px);
                }
                to {
                    opacity: 1;
                    transform: translateX(0);
                }
            }

            #characterDropdown, #voiceDropdown {
                appearance: none;
                -webkit-appearance: none;
                -moz-appearance: none;
                padding: 16px 55px 16px 22px;
                font-size: 15px;
                font-weight: 500;
                color: #ffffff;
                background: linear-gradient(135deg,
                    rgba(255, 255, 255, 0.12) 0%,
                    rgba(255, 255, 255, 0.06) 50%,
                    rgba(120, 219, 255, 0.08) 100%);
                border: 1px solid rgba(255, 255, 255, 0.25);
                border-radius: 14px;
                box-shadow:
                    0 10px 35px rgba(0, 0, 0, 0.25),
                    inset 0 1px 0 rgba(255, 255, 255, 0.25),
                    0 0 0 1px rgba(120, 219, 255, 0.1);
                backdrop-filter: blur(25px);
                cursor: pointer;
                outline: none;
                transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
                min-width: 150px;
                font-family: inherit;
                position: relative;
                overflow: hidden;
            }

            /* 添加内部光效 */
            #characterDropdown::before, #voiceDropdown::before {
                content: '';
                position: absolute;
                top: 0;
                left: -100%;
                width: 100%;
                height: 100%;
                background: linear-gradient(90deg,
                    transparent,
                    rgba(255, 255, 255, 0.1),
                    transparent);
                transition: left 0.6s ease;
                pointer-events: none;
            }

            #characterDropdown:hover, #voiceDropdown:hover {
                background: linear-gradient(135deg,
                    rgba(255, 255, 255, 0.18) 0%,
                    rgba(255, 255, 255, 0.1) 50%,
                    rgba(120, 219, 255, 0.12) 100%);
                border-color: rgba(120, 219, 255, 0.4);
                box-shadow:
                    0 15px 45px rgba(0, 0, 0, 0.35),
                    inset 0 1px 0 rgba(255, 255, 255, 0.35),
                    0 0 0 1px rgba(120, 219, 255, 0.2),
                    0 0 20px rgba(120, 219, 255, 0.15);
                transform: translateY(-3px) scale(1.02);
            }

            #characterDropdown:hover::before, #voiceDropdown:hover::before {
                left: 100%;
            }

            #characterDropdown:focus, #voiceDropdown:focus {
                background: linear-gradient(135deg,
                    rgba(255, 255, 255, 0.22) 0%,
                    rgba(255, 255, 255, 0.12) 50%,
                    rgba(120, 219, 255, 0.15) 100%);
                border-color: rgba(120, 219, 255, 0.7);
                box-shadow:
                    0 15px 45px rgba(0, 0, 0, 0.35),
                    0 0 0 3px rgba(120, 219, 255, 0.3),
                    inset 0 1px 0 rgba(255, 255, 255, 0.4),
                    0 0 25px rgba(120, 219, 255, 0.2);
                transform: translateY(-3px) scale(1.02);
            }

            #characterDropdown option, #voiceDropdown option {
                background: linear-gradient(135deg,
                    rgba(30, 30, 60, 0.98) 0%,
                    rgba(40, 40, 80, 0.95) 100%);
                color: #ffffff;
                padding: 12px 16px;
                border: none;
                font-weight: 500;
                transition: all 0.2s ease;
            }

            #characterDropdown option:hover, #voiceDropdown option:hover {
                background: linear-gradient(135deg,
                    rgba(120, 219, 255, 0.2) 0%,
                    rgba(147, 51, 234, 0.2) 100%);
                color: #ffffff;
            }

            #dropdownContainer::after, #voiceDropdownContainer::after {
                content: '▼';
                position: absolute;
                top: 50%;
                right: 20px;
                transform: translateY(-50%);
                pointer-events: none;
                color: rgba(255, 255, 255, 0.8);
                font-size: 13px;
                transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
                text-shadow: 0 0 10px rgba(120, 219, 255, 0.3);
            }

            #dropdownContainer:hover::after, #voiceDropdownContainer:hover::after {
                color: rgba(120, 219, 255, 1);
                transform: translateY(-50%) scale(1.15) rotate(180deg);
                text-shadow: 0 0 15px rgba(120, 219, 255, 0.6);
            }

            /* 为下拉容器添加标签效果 */
            #dropdownContainer::before {
                content: '角色选择';
                position: absolute;
                top: -8px;
                left: 12px;
                background: linear-gradient(135deg, rgba(120, 219, 255, 0.9), rgba(147, 51, 234, 0.9));
                color: #ffffff;
                padding: 2px 8px;
                border-radius: 6px;
                font-size: 11px;
                font-weight: 600;
                z-index: 10;
                opacity: 0;
                transform: translateY(-5px);
                transition: all 0.3s ease;
                pointer-events: none;
            }

            #voiceDropdownContainer::before {
                content: '语音选择';
                position: absolute;
                top: -8px;
                left: 12px;
                background: linear-gradient(135deg, rgba(255, 119, 198, 0.9), rgba(120, 219, 255, 0.9));
                color: #ffffff;
                padding: 2px 8px;
                border-radius: 6px;
                font-size: 11px;
                font-weight: 600;
                z-index: 10;
                opacity: 0;
                transform: translateY(-5px);
                transition: all 0.3s ease;
                pointer-events: none;
            }

            #dropdownContainer:hover::before, #voiceDropdownContainer:hover::before {
                opacity: 1;
                transform: translateY(0);
            }
            #canvasEl {
                position: absolute;
                left: -9999px;
                top: -9999px;
                width: 300px;
                height: 150px;
            }

            /* 增强科技感加载动画 */
            #loadingSpinner {
                position: relative;
                margin: 0;
                padding: 24px 36px;
                background: linear-gradient(135deg,
                    rgba(255, 255, 255, 0.12) 0%,
                    rgba(255, 255, 255, 0.06) 50%,
                    rgba(120, 219, 255, 0.08) 100%);
                border: 1px solid rgba(255, 255, 255, 0.25);
                border-radius: 20px;
                box-shadow:
                    0 12px 40px rgba(0, 0, 0, 0.3),
                    inset 0 1px 0 rgba(255, 255, 255, 0.25),
                    0 0 0 1px rgba(120, 219, 255, 0.2);
                backdrop-filter: blur(25px);
                color: #ffffff;
                font-size: 19px;
                font-weight: 600;
                text-shadow: 0 2px 15px rgba(0, 0, 0, 0.4);
                animation: loadingPulse 2s ease-in-out infinite;
                z-index: 100;
                overflow: hidden;
            }

            #loadingSpinner::before {
                content: '';
                position: absolute;
                top: -3px;
                left: -3px;
                right: -3px;
                bottom: -3px;
                background: linear-gradient(45deg,
                    #667eea, #764ba2, #78dbff, #9333ea, #667eea);
                background-size: 300% 300%;
                border-radius: 23px;
                z-index: -1;
                animation: borderGlow 3s linear infinite;
                opacity: 0.8;
            }

            /* 添加内部扫描线效果 */
            #loadingSpinner::after {
                content: '';
                position: absolute;
                top: 0;
                left: -100%;
                width: 100%;
                height: 100%;
                background: linear-gradient(90deg,
                    transparent,
                    rgba(120, 219, 255, 0.3),
                    transparent);
                animation: scanLine 2s ease-in-out infinite;
                pointer-events: none;
            }

            @keyframes loadingPulse {
                0%, 100% {
                    transform: scale(1);
                    opacity: 0.95;
                }
                50% {
                    transform: scale(1.03);
                    opacity: 1;
                }
            }

            @keyframes borderGlow {
                0% { background-position: 0% 50%; }
                50% { background-position: 100% 50%; }
                100% { background-position: 0% 50%; }
            }

            @keyframes scanLine {
                0% { left: -100%; }
                50% { left: 100%; }
                100% { left: 100%; }
            }

            /* 响应式设计 */
            @media (max-width: 768px) {
                #dropdownContainer, #voiceDropdownContainer {
                    left: 16px;
                    top: 16px;
                }

                #voiceDropdownContainer {
                    top: 70px;
                }

                #characterDropdown, #voiceDropdown {
                    padding: 12px 40px 12px 16px;
                    font-size: 14px;
                    min-width: 120px;
                }

                #startMessage {
                    font-size: 24px;
                }

                #loadingSpinner {
                    padding: 16px 24px;
                    font-size: 16px;
                }
            }

            @media (max-width: 480px) {
                #dropdownContainer, #voiceDropdownContainer {
                    left: 12px;
                    top: 12px;
                }

                #voiceDropdownContainer {
                    top: 60px;
                }

                #characterDropdown, #voiceDropdown {
                    padding: 10px 35px 10px 14px;
                    font-size: 13px;
                    min-width: 100px;
                }

                #startMessage {
                    font-size: 20px;
                }

                #loadingSpinner {
                    padding: 14px 20px;
                    font-size: 15px;
                }
            }
        </style>
    </head>
    <body>
        <div id="dropdownContainer">
            <select id="characterDropdown">
                <option value="assets">男性一</option>
                <option value="assets2">女性一</option>
            </select>
        </div>
        <div id="voiceDropdownContainer">
            <select id="voiceDropdown">
                <option value=0>温柔女</option>
                <option value=1>温柔男</option>
                <option value=2>甜美女</option>
                <option value=3>青年女</option>
                <option value=4>磁性男</option>
            </select>
        </div>

        <figure style="overflow:visible;" id="loadingSpinner">
            <strong>MiniMates: loading...</strong>
        </figure>
        <canvas id="canvasEl"></canvas>
        <canvas id="canvas_video"></canvas>
        <canvas id="canvas_gl" width="128" height="128"></canvas>
        <div id="screen"></div>
        <iframe id="screen2" src="dialog_RealTime.html" frameborder="0" style="display: none;"></iframe>
        <div id="startMessage">加载中</div>
        <script src="js/pako.min.js"></script>
        <script src="js/mp4box.all.min.js"></script>
        <script src="js/DHLiveMini.js"></script>
        <script src="js/MiniMateLoader.js"></script>
        <script src="js/MiniLive2.js"></script>
    </body>
</html>