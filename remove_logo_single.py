#!/usr/bin/env python3
"""
单个文件logo去除脚本
修改指定的combined_data.json.gz文件中的authorized字段
"""

import os
import gzip
import json
import shutil
import sys

def remove_logo_from_file(file_path):
    """
    从指定的combined_data.json.gz文件中去除logo
    
    Args:
        file_path (str): combined_data.json.gz文件的路径
    
    Returns:
        bool: 操作是否成功
    """
    if not os.path.exists(file_path):
        print(f"错误: 文件不存在 - {file_path}")
        return False
    
    if not file_path.endswith('.gz'):
        print(f"错误: 文件不是.gz格式 - {file_path}")
        return False
    
    try:
        # 备份原文件
        backup_path = file_path + ".backup"
        shutil.copy2(file_path, backup_path)
        print(f"已创建备份文件: {backup_path}")
        
        # 读取并解析文件
        print("正在读取文件...")
        with gzip.open(file_path, 'rt', encoding='UTF-8') as f:
            combined_data = json.load(f)
        
        # 显示文件信息
        print(f"文件信息:")
        print(f"  UID: {combined_data.get('uid', 'N/A')}")
        print(f"  帧数: {combined_data.get('frame_num', 'N/A')}")
        print(f"  当前授权状态: {combined_data.get('authorized', False)}")
        
        # 检查是否需要修改
        if combined_data.get("authorized", False):
            print("文件已经是授权状态，logo已经被去除")
            os.remove(backup_path)  # 删除不必要的备份
            return True
        
        # 修改authorized字段
        print("正在修改authorized字段...")
        combined_data["authorized"] = True
        
        # 写回文件
        print("正在保存修改...")
        with gzip.open(file_path, 'wt', encoding='UTF-8') as f:
            json.dump(combined_data, f)
        
        # 验证修改
        print("正在验证修改...")
        with gzip.open(file_path, 'rt', encoding='UTF-8') as f:
            verify_data = json.load(f)
        
        if verify_data.get("authorized", False):
            print("✓ 修改成功！Logo已被去除")
            print(f"✓ 新的授权状态: {verify_data.get('authorized')}")
            os.remove(backup_path)  # 删除备份文件
            return True
        else:
            print("✗ 验证失败，正在恢复备份...")
            shutil.move(backup_path, file_path)
            return False
            
    except json.JSONDecodeError as e:
        print(f"错误: JSON解析失败 - {e}")
        if os.path.exists(backup_path):
            shutil.move(backup_path, file_path)
        return False
    except Exception as e:
        print(f"错误: 处理文件时出现异常 - {e}")
        if os.path.exists(backup_path):
            shutil.move(backup_path, file_path)
        return False

def main():
    """主函数"""
    print("=== 数字人形象Logo去除工具（单文件版）===")
    
    # 检查命令行参数
    if len(sys.argv) != 2:
        print("用法: python remove_logo_single.py <combined_data.json.gz文件路径>")
        print("\n示例:")
        print("  python remove_logo_single.py web_demo/static/assets/combined_data.json.gz")
        print("  python remove_logo_single.py web_demo/static/assets2/combined_data.json.gz")
        print("  python remove_logo_single.py web_demo/static/assets3/combined_data.json.gz")
        return
    
    file_path = sys.argv[1]
    
    print(f"目标文件: {file_path}")
    
    # 确认操作
    print("\n警告: 此操作将修改文件内容以去除logo")
    print("操作前会自动创建备份文件")
    choice = input("是否继续？(y/N): ").lower().strip()
    
    if choice not in ['y', 'yes']:
        print("操作已取消")
        return
    
    # 执行去除logo操作
    if remove_logo_from_file(file_path):
        print(f"\n🎉 成功！文件 {file_path} 的logo已被去除")
        print("\n下一步:")
        print("1. 重启web服务器（如果正在运行）")
        print("2. 刷新浏览器页面")
        print("3. 检查数字人形象是否还显示logo")
    else:
        print(f"\n❌ 失败！无法去除文件 {file_path} 的logo")

if __name__ == "__main__":
    main()
