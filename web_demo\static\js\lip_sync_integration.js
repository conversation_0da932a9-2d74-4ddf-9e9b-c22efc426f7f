/**
 * 嘴唇同步集成脚本
 * 将自定义嘴唇同步系统集成到现有的渲染流程中
 */

console.log('🔗 嘴唇同步集成脚本已加载');

window.LipSyncIntegration = {
    isEnabled: false,
    originalUpdateBlendShape: null,
    customLipSync: null
};

/**
 * 替换原有的updateBlendShape函数
 */
function replaceBlendShapeFunction() {
    if (!Module || !Module._updateBlendShape) {
        console.log('⏳ 等待WASM模块加载...');
        setTimeout(replaceBlendShapeFunction, 1000);
        return;
    }
    
    console.log('🔧 替换BlendShape函数');
    
    // 保存原始函数
    window.LipSyncIntegration.originalUpdateBlendShape = Module._updateBlendShape;
    
    // 创建新的函数
    Module._updateBlendShape = function(bsPtr, size) {
        if (window.LipSyncIntegration.isEnabled && window.customLipSync && window.customLipSync.isActive) {
            // 使用自定义嘴唇同步数据
            const customShape = window.customLipSync.getLipShapeArray();
            
            // 将自定义数据写入WASM内存
            const bsArray = new Float32Array(Module.HEAPU8.buffer, bsPtr, 12);
            
            // 复制自定义嘴唇数据
            for (let i = 0; i < Math.min(customShape.length, bsArray.length); i++) {
                bsArray[i] = customShape[i];
            }
            
            // 调试输出
            if (Math.random() < 0.01) { // 1%的概率输出调试信息
                console.log('🎤 使用自定义嘴唇数据:', Array.from(customShape.slice(0, 6)).map(v => v.toFixed(3)));
            }
        } else {
            // 使用原始函数
            return window.LipSyncIntegration.originalUpdateBlendShape(bsPtr, size);
        }
    };
    
    console.log('✅ BlendShape函数替换完成');
}

/**
 * 启用自定义嘴唇同步
 */
async function enableCustomLipSync() {
    console.log('🚀 启用自定义嘴唇同步');
    
    if (!window.customLipSync) {
        console.error('❌ 自定义嘴唇同步系统未加载');
        return false;
    }
    
    // 启动自定义嘴唇同步
    const success = await window.customLipSync.start();
    if (!success) {
        console.error('❌ 自定义嘴唇同步启动失败');
        return false;
    }
    
    // 启用集成
    window.LipSyncIntegration.isEnabled = true;
    
    console.log('✅ 自定义嘴唇同步已启用');
    return true;
}

/**
 * 禁用自定义嘴唇同步
 */
function disableCustomLipSync() {
    console.log('⏹️ 禁用自定义嘴唇同步');
    
    // 停止自定义嘴唇同步
    if (window.customLipSync) {
        window.customLipSync.stop();
    }
    
    // 禁用集成
    window.LipSyncIntegration.isEnabled = false;
    
    console.log('✅ 自定义嘴唇同步已禁用，恢复原始系统');
}

/**
 * 恢复原始BlendShape函数
 */
function restoreOriginalBlendShape() {
    if (window.LipSyncIntegration.originalUpdateBlendShape) {
        Module._updateBlendShape = window.LipSyncIntegration.originalUpdateBlendShape;
        console.log('🔄 已恢复原始BlendShape函数');
    }
}

/**
 * 获取系统状态
 */
function getSystemStatus() {
    return {
        integrationEnabled: window.LipSyncIntegration.isEnabled,
        customLipSyncActive: window.customLipSync ? window.customLipSync.isActive : false,
        hasOriginalFunction: !!window.LipSyncIntegration.originalUpdateBlendShape,
        wasmReady: typeof Module !== 'undefined' && !!Module._updateBlendShape
    };
}

/**
 * 创建控制面板
 */
function createControlPanel() {
    // 检查是否已存在控制面板
    if (document.getElementById('lipSyncControlPanel')) {
        return;
    }
    
    const panel = document.createElement('div');
    panel.id = 'lipSyncControlPanel';
    panel.style.cssText = `
        position: fixed;
        top: 10px;
        right: 10px;
        background: rgba(0, 0, 0, 0.8);
        color: white;
        padding: 15px;
        border-radius: 10px;
        font-family: monospace;
        font-size: 12px;
        z-index: 10000;
        min-width: 250px;
    `;
    
    panel.innerHTML = `
        <h3 style="margin: 0 0 10px 0; color: #00ff88;">🎤 嘴唇同步控制</h3>
        <div style="margin-bottom: 10px;">
            <button id="startLipSync" style="margin-right: 5px; padding: 5px 10px; background: #28a745; color: white; border: none; border-radius: 3px; cursor: pointer;">启动</button>
            <button id="stopLipSync" style="margin-right: 5px; padding: 5px 10px; background: #dc3545; color: white; border: none; border-radius: 3px; cursor: pointer;">停止</button>
            <button id="debugLipSync" style="padding: 5px 10px; background: #17a2b8; color: white; border: none; border-radius: 3px; cursor: pointer;">调试</button>
        </div>
        <div id="lipSyncStatus" style="font-size: 11px; color: #ccc;">
            状态: 未启动
        </div>
        <div id="lipSyncDebug" style="font-size: 10px; color: #888; margin-top: 5px; max-height: 100px; overflow-y: auto;">
            等待调试信息...
        </div>
    `;
    
    document.body.appendChild(panel);
    
    // 绑定事件
    document.getElementById('startLipSync').onclick = async () => {
        const success = await enableCustomLipSync();
        updateControlPanel();
    };
    
    document.getElementById('stopLipSync').onclick = () => {
        disableCustomLipSync();
        updateControlPanel();
    };
    
    document.getElementById('debugLipSync').onclick = () => {
        if (window.customLipSync) {
            const debug = window.customLipSync.getDebugInfo();
            document.getElementById('lipSyncDebug').innerHTML = `
                音量: ${debug.volume}<br>
                频率: ${debug.frequency}Hz<br>
                音素: ${debug.phoneme}<br>
                共振峰: ${debug.formants}<br>
                形状: [${debug.currentShape.join(', ')}]
            `;
        }
    };
    
    // 定期更新状态
    setInterval(updateControlPanel, 1000);
}

/**
 * 更新控制面板
 */
function updateControlPanel() {
    const statusElement = document.getElementById('lipSyncStatus');
    if (!statusElement) return;
    
    const status = getSystemStatus();
    let statusText = '';
    
    if (status.customLipSyncActive) {
        statusText = '🟢 自定义嘴唇同步运行中';
    } else if (status.integrationEnabled) {
        statusText = '🟡 集成已启用，等待音频';
    } else {
        statusText = '🔴 使用原始系统';
    }
    
    statusElement.innerHTML = `
        状态: ${statusText}<br>
        WASM: ${status.wasmReady ? '✅' : '❌'}<br>
        集成: ${status.integrationEnabled ? '✅' : '❌'}
    `;
}

// 导出控制接口
window.lipSyncIntegration = {
    enable: enableCustomLipSync,
    disable: disableCustomLipSync,
    restore: restoreOriginalBlendShape,
    status: getSystemStatus,
    createPanel: createControlPanel
};

// 页面加载完成后初始化
window.addEventListener('load', function() {
    console.log('📋 页面加载完成，初始化嘴唇同步集成');
    
    // 延迟初始化，确保所有脚本都已加载
    setTimeout(() => {
        replaceBlendShapeFunction();
        createControlPanel();
        
        console.log('✅ 嘴唇同步集成初始化完成');
        console.log('💡 使用方法:');
        console.log('  lipSyncIntegration.enable() - 启用自定义嘴唇同步');
        console.log('  lipSyncIntegration.disable() - 禁用自定义嘴唇同步');
        console.log('  lipSyncIntegration.status() - 查看系统状态');
        console.log('  或使用右上角的控制面板');
    }, 2000);
});

// 如果WASM模块已经准备就绪
if (typeof Module !== 'undefined' && Module.ready) {
    Module.ready.then(() => {
        console.log('🎉 WASM模块ready事件触发');
        setTimeout(replaceBlendShapeFunction, 500);
    });
}
