<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Logo绕过深度调试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: #1a1a1a;
            color: #fff;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: #2d2d2d;
            border-radius: 10px;
        }
        
        .panel {
            background: #2d2d2d;
            margin-bottom: 20px;
            padding: 20px;
            border-radius: 10px;
            border-left: 4px solid #007bff;
        }
        
        .control-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }
        
        .btn {
            padding: 12px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-weight: bold;
            transition: all 0.3s;
        }
        
        .btn-primary { background: #007bff; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-warning { background: #ffc107; color: black; }
        .btn-danger { background: #dc3545; color: white; }
        .btn-info { background: #17a2b8; color: white; }
        
        .btn:hover { opacity: 0.8; transform: translateY(-2px); }
        
        .log-panel {
            background: #000;
            color: #00ff00;
            padding: 20px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            height: 400px;
            overflow-y: auto;
            border: 1px solid #333;
        }
        
        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }
        
        .status-card {
            background: #3d3d3d;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #28a745;
        }
        
        .status-card h4 {
            margin: 0 0 10px 0;
            color: #fff;
        }
        
        .status-value {
            font-size: 18px;
            font-weight: bold;
            color: #00ff88;
        }
        
        .analysis-panel {
            background: #2d2d2d;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
        }
        
        .code-block {
            background: #1e1e1e;
            padding: 15px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            overflow-x: auto;
            border: 1px solid #444;
        }
        
        .highlight {
            background: #ffff0020;
            padding: 2px 4px;
            border-radius: 3px;
        }
        
        .demo-frame {
            width: 100%;
            height: 500px;
            border: 2px solid #444;
            border-radius: 10px;
            background: #000;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔍 Logo绕过深度调试系统</h1>
            <p>分析logo和嘴唇渲染的具体机制，找出问题根源</p>
        </div>
        
        <div class="panel">
            <h3>🎯 当前问题分析</h3>
            <p><strong>现象：</strong> 绕过logo后，嘴唇也不显示了</p>
            <p><strong>推测：</strong> logo和嘴唇可能在同一个渲染管道中，或者授权验证影响了整个渲染流程</p>
            <p><strong>目标：</strong> 找到只去除logo但保留嘴唇的精确方法</p>
        </div>
        
        <div class="control-grid">
            <button class="btn btn-primary" onclick="startDeepAnalysis()">🔍 开始深度分析</button>
            <button class="btn btn-success" onclick="testOriginal()">🎬 测试原版</button>
            <button class="btn btn-warning" onclick="testWithBypass()">⚡ 测试绕过版</button>
            <button class="btn btn-info" onclick="compareResults()">📊 对比结果</button>
            <button class="btn btn-danger" onclick="exportData()">💾 导出数据</button>
            <button class="btn btn-primary" onclick="clearLogs()">🧹 清空日志</button>
        </div>
        
        <div class="status-grid">
            <div class="status-card">
                <h4>WASM模块状态</h4>
                <div class="status-value" id="wasm-status">检测中...</div>
            </div>
            <div class="status-card">
                <h4>拦截器状态</h4>
                <div class="status-value" id="interceptor-status">未安装</div>
            </div>
            <div class="status-card">
                <h4>调用次数</h4>
                <div class="status-value" id="call-count">0</div>
            </div>
            <div class="status-card">
                <h4>检测到的问题</h4>
                <div class="status-value" id="issues-count">0</div>
            </div>
        </div>
        
        <div class="analysis-panel">
            <h3>📋 实时分析结果</h3>
            <div id="analysis-results">
                <p>等待开始分析...</p>
            </div>
        </div>
        
        <div class="panel">
            <h3>🖥️ 实时日志</h3>
            <div class="log-panel" id="log-panel">
                <div>[系统] 深度调试系统已启动</div>
                <div>[提示] 点击"开始深度分析"按钮开始调试</div>
            </div>
        </div>
        
        <div class="panel">
            <h3>🎬 测试区域</h3>
            <p>在这里可以直接测试不同的配置：</p>
            <iframe id="demo-frame" class="demo-frame" style="display: none;"></iframe>
            <div id="demo-placeholder" style="text-align: center; padding: 50px; color: #666;">
                点击测试按钮加载Demo
            </div>
        </div>
    </div>

    <!-- 加载脚本 -->
    <script src="js/pako.min.js"></script>
    <script src="js/mp4box.all.min.js"></script>
    <script src="js/DHLiveMini.js"></script>
    <script src="js/MiniMateLoader.js"></script>
    <script src="js/MiniLive2.js"></script>
    <script src="../debug_analysis.js"></script>

    <script>
        let analysisData = {
            original: null,
            bypassed: null,
            comparison: null
        };
        
        // 日志管理
        function addLog(message, type = 'info') {
            const logPanel = document.getElementById('log-panel');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            
            const colors = {
                info: '#00ff00',
                success: '#00ff88',
                warning: '#ffaa00',
                error: '#ff4444',
                data: '#ff88ff'
            };
            
            logEntry.style.color = colors[type] || colors.info;
            logEntry.innerHTML = `[${timestamp}] ${message}`;
            logPanel.appendChild(logEntry);
            logPanel.scrollTop = logPanel.scrollHeight;
        }
        
        // 更新状态显示
        function updateStatus() {
            // WASM模块状态
            const wasmStatus = document.getElementById('wasm-status');
            if (typeof Module !== 'undefined' && Module._processJson) {
                wasmStatus.textContent = '✅ 已加载';
                wasmStatus.style.color = '#00ff88';
            } else {
                wasmStatus.textContent = '❌ 未加载';
                wasmStatus.style.color = '#ff4444';
            }
            
            // 拦截器状态
            const interceptorStatus = document.getElementById('interceptor-status');
            if (window.DebugAnalysis && window.DebugAnalysis.isActive) {
                interceptorStatus.textContent = '✅ 已安装';
                interceptorStatus.style.color = '#00ff88';
            } else {
                interceptorStatus.textContent = '❌ 未安装';
                interceptorStatus.style.color = '#ff4444';
            }
            
            // 调用次数
            const callCount = document.getElementById('call-count');
            if (window.DebugAnalysis && window.DebugAnalysis.interceptedCalls) {
                callCount.textContent = window.DebugAnalysis.interceptedCalls.length;
            }
        }
        
        // 控制函数
        function startDeepAnalysis() {
            addLog('🔍 开始深度分析...', 'info');
            
            if (window.DebugAnalysis) {
                window.DebugAnalysis.clear();
                window.DebugAnalysis.install();
                addLog('✅ 调试拦截器已安装', 'success');
                
                // 开始监控
                setTimeout(() => {
                    addLog('📊 开始监控WASM调用...', 'info');
                    updateAnalysisResults();
                }, 1000);
            } else {
                addLog('❌ 调试系统未加载', 'error');
            }
        }
        
        function testOriginal() {
            addLog('🎬 测试原版（无绕过）...', 'info');
            
            // 禁用所有绕过
            if (window.MinimalBypass) {
                window.MinimalBypass.uninstall();
            }
            
            // 加载原版Demo
            const frame = document.getElementById('demo-frame');
            const placeholder = document.getElementById('demo-placeholder');
            
            frame.src = 'MiniLive.html?mode=original&t=' + Date.now();
            frame.style.display = 'block';
            placeholder.style.display = 'none';
            
            addLog('📺 原版Demo已加载', 'success');
            
            // 收集数据
            setTimeout(() => {
                if (window.DebugAnalysis) {
                    analysisData.original = window.DebugAnalysis.analyze();
                    addLog('📊 原版数据已收集', 'data');
                }
            }, 3000);
        }
        
        function testWithBypass() {
            addLog('⚡ 测试绕过版...', 'warning');
            
            // 启用绕过
            if (window.MinimalBypass) {
                window.MinimalBypass.install();
            }
            
            // 加载绕过版Demo
            const frame = document.getElementById('demo-frame');
            frame.src = 'MiniLive.html?mode=bypass&t=' + Date.now();
            
            addLog('📺 绕过版Demo已加载', 'success');
            
            // 收集数据
            setTimeout(() => {
                if (window.DebugAnalysis) {
                    analysisData.bypassed = window.DebugAnalysis.analyze();
                    addLog('📊 绕过版数据已收集', 'data');
                }
            }, 3000);
        }
        
        function compareResults() {
            addLog('📊 开始对比分析...', 'info');
            
            if (!analysisData.original || !analysisData.bypassed) {
                addLog('❌ 需要先测试原版和绕过版', 'error');
                return;
            }
            
            const comparison = {
                originalCalls: analysisData.original.totalCalls,
                bypassedCalls: analysisData.bypassed.totalCalls,
                difference: analysisData.bypassed.totalCalls - analysisData.original.totalCalls
            };
            
            analysisData.comparison = comparison;
            
            const resultsDiv = document.getElementById('analysis-results');
            resultsDiv.innerHTML = `
                <h4>🔍 对比分析结果</h4>
                <div class="code-block">
                    <div>原版调用次数: ${comparison.originalCalls}</div>
                    <div>绕过版调用次数: ${comparison.bypassedCalls}</div>
                    <div class="highlight">调用差异: ${comparison.difference}</div>
                </div>
                <p><strong>分析建议：</strong></p>
                <ul>
                    <li>如果调用次数相同，问题可能在参数处理</li>
                    <li>如果调用次数不同，可能某些函数被跳过了</li>
                    <li>需要检查具体的JSON和图像数据</li>
                </ul>
            `;
            
            addLog('✅ 对比分析完成', 'success');
        }
        
        function exportData() {
            if (window.DebugAnalysis) {
                window.DebugAnalysis.export();
                addLog('💾 调试数据已导出', 'success');
            } else {
                addLog('❌ 无数据可导出', 'error');
            }
        }
        
        function clearLogs() {
            document.getElementById('log-panel').innerHTML = '';
            if (window.DebugAnalysis) {
                window.DebugAnalysis.clear();
            }
            addLog('🧹 日志已清空', 'info');
        }
        
        function updateAnalysisResults() {
            if (!window.DebugAnalysis) return;
            
            const calls = window.DebugAnalysis.interceptedCalls;
            const resultsDiv = document.getElementById('analysis-results');
            
            if (calls.length === 0) {
                resultsDiv.innerHTML = '<p>暂无调用数据，请加载Demo进行测试</p>';
                return;
            }
            
            const jsonCalls = calls.filter(c => c.message.includes('processJson'));
            const imageCalls = calls.filter(c => c.message.includes('processImage'));
            
            resultsDiv.innerHTML = `
                <h4>📊 实时调用统计</h4>
                <div class="code-block">
                    <div>总调用次数: ${calls.length}</div>
                    <div>processJson调用: ${jsonCalls.length}</div>
                    <div>processImage调用: ${imageCalls.length}</div>
                </div>
                <h4>🔍 最近的调用</h4>
                <div class="code-block">
                    ${calls.slice(-5).map(call => 
                        `<div>[${call.timestamp}] ${call.message}</div>`
                    ).join('')}
                </div>
            `;
        }
        
        // 页面初始化
        window.addEventListener('load', function() {
            addLog('📋 深度调试页面已加载', 'success');
            
            // 定期更新状态
            setInterval(updateStatus, 1000);
            setInterval(updateAnalysisResults, 2000);
            
            // 监听控制台输出
            const originalLog = console.log;
            console.log = function(...args) {
                const message = args.join(' ');
                if (message.includes('🔍 调试分析')) {
                    addLog(message.replace('🔍 调试分析: ', ''), 'data');
                }
                originalLog.apply(console, args);
            };
            
            addLog('🎯 深度调试系统已就绪', 'success');
            addLog('💡 建议：先测试原版，再测试绕过版，然后对比结果', 'info');
        });
    </script>
</body>
</html>
