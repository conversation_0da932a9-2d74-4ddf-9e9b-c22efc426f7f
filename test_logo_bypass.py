#!/usr/bin/env python3
"""
测试logo绕过方案
启动web服务器并自动打开浏览器进行测试
"""

import os
import sys
import time
import threading
import webbrowser
from http.server import HTTPServer, SimpleHTTPRequestHandler
import subprocess

class CustomHTTPRequestHandler(SimpleHTTPRequestHandler):
    """自定义HTTP请求处理器，添加CORS支持"""
    
    def end_headers(self):
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        super().end_headers()
    
    def log_message(self, format, *args):
        """自定义日志格式"""
        print(f"[{time.strftime('%H:%M:%S')}] {format % args}")

def start_web_server(port=8888, directory="web_demo/static"):
    """启动Web服务器"""
    try:
        os.chdir(directory)
        print(f"🌐 启动Web服务器在端口 {port}...")
        print(f"📁 服务目录: {os.getcwd()}")
        
        server = HTTPServer(('localhost', port), CustomHTTPRequestHandler)
        print(f"✅ 服务器已启动: http://localhost:{port}")
        print(f"🎯 测试页面: http://localhost:{port}/MiniLive.html")
        
        # 在新线程中启动服务器
        server_thread = threading.Thread(target=server.serve_forever)
        server_thread.daemon = True
        server_thread.start()
        
        return server
        
    except Exception as e:
        print(f"❌ 启动服务器失败: {e}")
        return None

def check_files():
    """检查必要的文件是否存在"""
    print("🔍 检查必要文件...")
    
    required_files = [
        "web_demo/static/MiniLive.html",
        "web_demo/static/js/DHLiveMini.js",
        "web_demo/static/js/MiniLive2.js",
        "web_demo/static/DHLiveMini.wasm",
        "web_demo/static/assets/combined_data.json.gz"
    ]
    
    missing_files = []
    for file_path in required_files:
        if not os.path.exists(file_path):
            missing_files.append(file_path)
        else:
            print(f"✅ {file_path}")
    
    if missing_files:
        print(f"\n❌ 缺少以下文件:")
        for file_path in missing_files:
            print(f"   - {file_path}")
        return False
    
    print("✅ 所有必要文件都存在")
    return True

def check_modifications():
    """检查我们的修改是否已应用"""
    print("\n🔍 检查代码修改...")
    
    # 检查MiniLive2.js的修改
    try:
        with open("web_demo/static/js/MiniLive2.js", 'r', encoding='utf-8') as f:
            content = f.read()
            if "WasmInputJson.authorized = true;" in content:
                print("✅ MiniLive2.js 修改已应用")
            else:
                print("❌ MiniLive2.js 修改未找到")
    except Exception as e:
        print(f"❌ 检查MiniLive2.js失败: {e}")
    
    # 检查DHLiveMini.js的修改
    try:
        with open("web_demo/static/js/DHLiveMini.js", 'r', encoding='utf-8') as f:
            content = f.read()
            if "拦截processJson调用，绕过logo验证" in content:
                print("✅ DHLiveMini.js processJson修改已应用")
            else:
                print("❌ DHLiveMini.js processJson修改未找到")
                
            if "拦截processImage调用，绕过logo渲染" in content:
                print("✅ DHLiveMini.js processImage修改已应用")
            else:
                print("❌ DHLiveMini.js processImage修改未找到")
    except Exception as e:
        print(f"❌ 检查DHLiveMini.js失败: {e}")

def open_browser_with_delay(url, delay=3):
    """延迟打开浏览器"""
    time.sleep(delay)
    print(f"🌐 打开浏览器: {url}")
    webbrowser.open(url)

def main():
    """主函数"""
    print("=" * 60)
    print("🎯 数字人形象Logo绕过测试工具")
    print("=" * 60)
    
    # 检查当前目录
    if not os.path.exists("web_demo"):
        print("❌ 请在项目根目录运行此脚本")
        return
    
    # 检查文件
    if not check_files():
        print("\n❌ 文件检查失败，请确保所有必要文件都存在")
        return
    
    # 检查修改
    check_modifications()
    
    # 启动服务器
    server = start_web_server()
    if not server:
        return
    
    # 延迟打开浏览器
    browser_thread = threading.Thread(
        target=open_browser_with_delay, 
        args=("http://localhost:8888/MiniLive.html", 3)
    )
    browser_thread.start()
    
    print("\n" + "=" * 60)
    print("🎉 测试环境已启动！")
    print("=" * 60)
    print("📋 测试步骤:")
    print("1. 浏览器将自动打开测试页面")
    print("2. 观察数字人形象是否显示logo")
    print("3. 检查浏览器控制台的日志输出")
    print("4. 查看是否有绕过相关的日志信息")
    print("\n🔍 预期结果:")
    print("- 控制台应显示 '🔧 拦截processJson调用，绕过logo验证'")
    print("- 控制台应显示 '🖼️ 拦截processImage调用，绕过logo渲染'")
    print("- 数字人形象应该不显示logo")
    print("\n⚠️  如果仍然显示logo，可能需要:")
    print("- 清除浏览器缓存")
    print("- 使用无痕模式")
    print("- 检查WebAssembly模块是否有其他验证机制")
    print("\n按 Ctrl+C 停止服务器")
    print("=" * 60)
    
    try:
        # 保持服务器运行
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        print("\n\n🛑 停止服务器...")
        server.shutdown()
        print("✅ 服务器已停止")

if __name__ == "__main__":
    main()
