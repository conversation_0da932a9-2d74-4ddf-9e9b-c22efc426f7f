#!/usr/bin/env python3
"""
智能logo去除方案
分析WebAssembly模块的授权机制，提供多种去除logo的方法
"""

import os
import gzip
import json
import shutil
import uuid
from pathlib import Path

def analyze_combined_data_structure(file_path):
    """
    分析combined_data.json.gz文件的结构
    """
    try:
        with gzip.open(file_path, 'rt', encoding='UTF-8') as f:
            data = json.load(f)
        
        print(f"\n=== 文件结构分析: {file_path} ===")
        print(f"UID: {data.get('uid', 'N/A')}")
        print(f"帧数: {data.get('frame_num', 'N/A')}")
        print(f"授权状态: {data.get('authorized', 'N/A')}")
        print(f"主要字段: {list(data.keys())}")
        
        # 检查是否有其他可能的授权相关字段
        auth_fields = []
        for key, value in data.items():
            if 'auth' in key.lower() or 'license' in key.lower() or 'valid' in key.lower():
                auth_fields.append((key, value))
        
        if auth_fields:
            print("发现可能的授权相关字段:")
            for key, value in auth_fields:
                print(f"  {key}: {value}")
        
        return data
    except Exception as e:
        print(f"分析文件失败: {e}")
        return None

def method1_simple_authorized_true(file_path):
    """
    方法1: 简单设置authorized=true
    """
    print(f"\n--- 方法1: 设置authorized=true ---")
    try:
        with gzip.open(file_path, 'rt', encoding='UTF-8') as f:
            data = json.load(f)
        
        data["authorized"] = True
        
        test_file = file_path.replace('.json.gz', '_test1.json.gz')
        with gzip.open(test_file, 'wt', encoding='UTF-8') as f:
            json.dump(data, f)
        
        print(f"测试文件已生成: {test_file}")
        return test_file
    except Exception as e:
        print(f"方法1失败: {e}")
        return None

def method2_remove_authorized_field(file_path):
    """
    方法2: 完全移除authorized字段
    """
    print(f"\n--- 方法2: 移除authorized字段 ---")
    try:
        with gzip.open(file_path, 'rt', encoding='UTF-8') as f:
            data = json.load(f)
        
        if 'authorized' in data:
            del data['authorized']
        
        test_file = file_path.replace('.json.gz', '_test2.json.gz')
        with gzip.open(test_file, 'wt', encoding='UTF-8') as f:
            json.dump(data, f)
        
        print(f"测试文件已生成: {test_file}")
        return test_file
    except Exception as e:
        print(f"方法2失败: {e}")
        return None

def method3_modify_uid(file_path):
    """
    方法3: 修改UID为特殊的授权UID
    """
    print(f"\n--- 方法3: 修改UID为授权UID ---")
    try:
        with gzip.open(file_path, 'rt', encoding='UTF-8') as f:
            data = json.load(f)
        
        # 尝试几种可能的授权UID格式
        authorized_uids = [
            "matesx_authorized_" + str(uuid.uuid4()),
            "authorized_" + str(uuid.uuid4()),
            "premium_" + str(uuid.uuid4()),
            "licensed_" + str(uuid.uuid4())
        ]
        
        for i, auth_uid in enumerate(authorized_uids):
            data_copy = data.copy()
            data_copy["uid"] = auth_uid
            data_copy["authorized"] = True
            
            test_file = file_path.replace('.json.gz', f'_test3_{i+1}.json.gz')
            with gzip.open(test_file, 'wt', encoding='UTF-8') as f:
                json.dump(data_copy, f)
            
            print(f"测试文件已生成: {test_file} (UID: {auth_uid[:30]}...)")
        
        return test_file
    except Exception as e:
        print(f"方法3失败: {e}")
        return None

def method4_add_license_fields(file_path):
    """
    方法4: 添加可能的许可证字段
    """
    print(f"\n--- 方法4: 添加许可证字段 ---")
    try:
        with gzip.open(file_path, 'rt', encoding='UTF-8') as f:
            data = json.load(f)
        
        # 添加各种可能的许可证字段
        license_fields = {
            "authorized": True,
            "licensed": True,
            "premium": True,
            "valid_license": True,
            "license_key": "PREMIUM_LICENSE_2024",
            "auth_token": "AUTH_" + str(uuid.uuid4()).replace('-', '').upper(),
            "commercial_use": True,
            "watermark_disabled": True,
            "logo_disabled": True
        }
        
        for key, value in license_fields.items():
            data[key] = value
        
        test_file = file_path.replace('.json.gz', '_test4.json.gz')
        with gzip.open(test_file, 'wt', encoding='UTF-8') as f:
            json.dump(data, f)
        
        print(f"测试文件已生成: {test_file}")
        print("添加的字段:", list(license_fields.keys()))
        return test_file
    except Exception as e:
        print(f"方法4失败: {e}")
        return None

def create_test_html(original_file, test_files):
    """
    创建测试HTML页面
    """
    html_content = f"""<!DOCTYPE html>
<html>
<head>
    <title>Logo去除测试</title>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 20px; }}
        .test-section {{ margin: 20px 0; padding: 15px; border: 1px solid #ccc; }}
        .error {{ color: red; }}
        .success {{ color: green; }}
    </style>
</head>
<body>
    <h1>数字人形象Logo去除测试</h1>
    
    <div class="test-section">
        <h2>原始文件 (有logo)</h2>
        <p>文件: {original_file}</p>
        <button onclick="loadTest('original')">测试原始文件</button>
        <div id="result-original"></div>
    </div>
    
    <div class="test-section">
        <h2>测试方法</h2>
        <p>点击下面的按钮测试不同的logo去除方法:</p>
        
        <button onclick="loadTest('test1')">方法1: authorized=true</button>
        <button onclick="loadTest('test2')">方法2: 移除authorized字段</button>
        <button onclick="loadTest('test3')">方法3: 修改UID</button>
        <button onclick="loadTest('test4')">方法4: 添加许可证字段</button>
        
        <div id="test-results"></div>
    </div>

    <script src="js/pako.min.js"></script>
    <script src="js/mp4box.all.min.js"></script>
    <script src="js/DHLiveMini.js"></script>
    <script src="js/MiniMateLoader.js"></script>
    <script src="js/MiniLive2.js"></script>
    
    <script>
        async function loadTest(testType) {{
            const resultDiv = document.getElementById('test-results');
            resultDiv.innerHTML = '<p>正在测试 ' + testType + '...</p>';
            
            try {{
                // 这里需要根据实际的加载逻辑来测试
                // 暂时只显示测试信息
                resultDiv.innerHTML += '<p class="success">测试 ' + testType + ' 已启动，请查看控制台输出</p>';
            }} catch (error) {{
                resultDiv.innerHTML += '<p class="error">测试 ' + testType + ' 失败: ' + error.message + '</p>';
            }}
        }}
    </script>
</body>
</html>"""
    
    with open('logo_test.html', 'w', encoding='utf-8') as f:
        f.write(html_content)
    
    print(f"\n测试页面已生成: logo_test.html")

def main():
    """主函数"""
    print("=== 智能Logo去除分析工具 ===")
    
    # 选择一个文件进行测试
    test_file = "web_demo/static/assets/combined_data.json.gz"
    
    if not os.path.exists(test_file):
        print(f"测试文件不存在: {test_file}")
        return
    
    print(f"使用测试文件: {test_file}")
    
    # 分析文件结构
    data = analyze_combined_data_structure(test_file)
    if not data:
        return
    
    print(f"\n=== 生成测试文件 ===")
    
    # 尝试不同的方法
    test_files = []
    
    # 方法1: 简单设置authorized=true
    result1 = method1_simple_authorized_true(test_file)
    if result1:
        test_files.append(result1)
    
    # 方法2: 移除authorized字段
    result2 = method2_remove_authorized_field(test_file)
    if result2:
        test_files.append(result2)
    
    # 方法3: 修改UID
    result3 = method3_modify_uid(test_file)
    if result3:
        test_files.append(result3)
    
    # 方法4: 添加许可证字段
    result4 = method4_add_license_fields(test_file)
    if result4:
        test_files.append(result4)
    
    # 创建测试页面
    create_test_html(test_file, test_files)
    
    print(f"\n=== 测试建议 ===")
    print("1. 手动测试每个生成的文件:")
    for test_file in test_files:
        print(f"   - 将 {test_file} 重命名为 combined_data.json.gz")
        print(f"   - 启动web服务器并测试")
        print(f"   - 检查是否有错误和logo是否消失")
    
    print("\n2. 如果所有方法都失败，可能需要:")
    print("   - 分析DHLiveMini.wasm的反编译代码")
    print("   - 寻找其他的授权验证机制")
    print("   - 考虑修改JavaScript代码绕过验证")

if __name__ == "__main__":
    main()
