/**
 * 自定义音频驱动嘴唇动画系统
 * 完全替代原有的WASM嘴唇控制，基于音频分析实现实时嘴唇同步
 */

class CustomLipSync {
    constructor() {
        this.audioContext = null;
        this.analyser = null;
        this.microphone = null;
        this.dataArray = null;
        this.isActive = false;
        this.lipSyncData = {
            volume: 0,
            frequency: 0,
            formants: [],
            phoneme: 'silence'
        };
        
        // 嘴唇形状参数 (对应bsArray的前几个元素)
        this.lipShapes = {
            silence: [0, 0, 0, 0, 0, 0],      // 闭嘴
            a: [0.8, 0.2, 0.1, 0.3, 0.0, 0.1], // 张嘴 "啊"
            e: [0.4, 0.6, 0.2, 0.1, 0.0, 0.2], // "诶"
            i: [0.2, 0.1, 0.8, 0.0, 0.0, 0.3], // "伊"
            o: [0.6, 0.3, 0.0, 0.7, 0.0, 0.1], // "哦"
            u: [0.3, 0.1, 0.0, 0.8, 0.0, 0.0], // "乌"
            m: [0.0, 0.0, 0.0, 0.0, 0.9, 0.0], // "嗯"
            f: [0.1, 0.0, 0.0, 0.0, 0.0, 0.8], // "夫"
        };
        
        this.currentShape = [...this.lipShapes.silence];
        this.targetShape = [...this.lipShapes.silence];
        this.smoothingFactor = 0.15; // 平滑过渡系数
        
        console.log('🎤 自定义嘴唇同步系统已初始化');
    }
    
    /**
     * 初始化音频分析
     */
    async initAudio() {
        try {
            console.log('🎤 初始化音频分析...');
            
            // 创建音频上下文
            this.audioContext = new (window.AudioContext || window.webkitAudioContext)();
            
            // 获取麦克风权限
            const stream = await navigator.mediaDevices.getUserMedia({ 
                audio: {
                    echoCancellation: true,
                    noiseSuppression: true,
                    autoGainControl: true
                } 
            });
            
            // 创建音频源
            this.microphone = this.audioContext.createMediaStreamSource(stream);
            
            // 创建分析器
            this.analyser = this.audioContext.createAnalyser();
            this.analyser.fftSize = 2048;
            this.analyser.smoothingTimeConstant = 0.8;
            
            // 连接音频流
            this.microphone.connect(this.analyser);
            
            // 创建数据数组
            this.dataArray = new Uint8Array(this.analyser.frequencyBinCount);
            
            console.log('✅ 音频分析初始化成功');
            return true;
            
        } catch (error) {
            console.error('❌ 音频初始化失败:', error);
            return false;
        }
    }
    
    /**
     * 分析音频数据
     */
    analyzeAudio() {
        if (!this.analyser || !this.dataArray) return;
        
        // 获取频域数据
        this.analyser.getByteFrequencyData(this.dataArray);
        
        // 计算音量 (RMS)
        let sum = 0;
        for (let i = 0; i < this.dataArray.length; i++) {
            sum += this.dataArray[i] * this.dataArray[i];
        }
        this.lipSyncData.volume = Math.sqrt(sum / this.dataArray.length) / 255;
        
        // 分析主要频率
        this.analyzeFrequency();
        
        // 根据音频特征确定音素
        this.detectPhoneme();
        
        // 更新嘴唇形状
        this.updateLipShape();
    }
    
    /**
     * 分析频率特征
     */
    analyzeFrequency() {
        const nyquist = this.audioContext.sampleRate / 2;
        const binSize = nyquist / this.dataArray.length;
        
        // 找到最强的频率
        let maxAmplitude = 0;
        let dominantFreq = 0;
        
        for (let i = 0; i < this.dataArray.length; i++) {
            if (this.dataArray[i] > maxAmplitude) {
                maxAmplitude = this.dataArray[i];
                dominantFreq = i * binSize;
            }
        }
        
        this.lipSyncData.frequency = dominantFreq;
        
        // 分析共振峰 (简化版)
        this.lipSyncData.formants = this.extractFormants();
    }
    
    /**
     * 提取共振峰 (简化实现)
     */
    extractFormants() {
        const formants = [];
        const binSize = (this.audioContext.sampleRate / 2) / this.dataArray.length;
        
        // 在不同频率范围寻找峰值
        const ranges = [
            [200, 800],   // F1
            [800, 2500],  // F2
            [2500, 4000]  // F3
        ];
        
        for (const [start, end] of ranges) {
            const startBin = Math.floor(start / binSize);
            const endBin = Math.floor(end / binSize);
            
            let maxAmp = 0;
            let peakFreq = 0;
            
            for (let i = startBin; i < endBin && i < this.dataArray.length; i++) {
                if (this.dataArray[i] > maxAmp) {
                    maxAmp = this.dataArray[i];
                    peakFreq = i * binSize;
                }
            }
            
            if (maxAmp > 50) { // 阈值过滤
                formants.push({ frequency: peakFreq, amplitude: maxAmp });
            }
        }
        
        return formants;
    }
    
    /**
     * 检测音素 (基于频率和音量)
     */
    detectPhoneme() {
        const { volume, frequency, formants } = this.lipSyncData;
        
        // 静音检测
        if (volume < 0.02) {
            this.lipSyncData.phoneme = 'silence';
            return;
        }
        
        // 基于共振峰的简单音素识别
        if (formants.length >= 2) {
            const f1 = formants[0].frequency;
            const f2 = formants[1].frequency;
            
            // 简化的音素分类
            if (f1 > 600 && f2 < 1200) {
                this.lipSyncData.phoneme = 'a'; // 低前元音
            } else if (f1 < 400 && f2 > 2000) {
                this.lipSyncData.phoneme = 'i'; // 高前元音
            } else if (f1 < 500 && f2 < 1000) {
                this.lipSyncData.phoneme = 'u'; // 高后元音
            } else if (f1 > 400 && f1 < 600 && f2 > 1200 && f2 < 2000) {
                this.lipSyncData.phoneme = 'e'; // 中前元音
            } else if (f1 > 500 && f2 < 1500) {
                this.lipSyncData.phoneme = 'o'; // 中后元音
            } else {
                this.lipSyncData.phoneme = 'a'; // 默认
            }
        } else {
            // 基于频率的简单分类
            if (frequency < 300) {
                this.lipSyncData.phoneme = 'u';
            } else if (frequency < 800) {
                this.lipSyncData.phoneme = 'o';
            } else if (frequency < 1500) {
                this.lipSyncData.phoneme = 'a';
            } else if (frequency < 2500) {
                this.lipSyncData.phoneme = 'e';
            } else {
                this.lipSyncData.phoneme = 'i';
            }
        }
        
        // 音量调制
        if (volume > 0.3) {
            // 大声时，可能是爆破音
            if (Math.random() > 0.7) {
                this.lipSyncData.phoneme = 'm';
            }
        }
    }
    
    /**
     * 更新嘴唇形状
     */
    updateLipShape() {
        const phoneme = this.lipSyncData.phoneme;
        const volume = this.lipSyncData.volume;
        
        // 获取目标形状
        if (this.lipShapes[phoneme]) {
            this.targetShape = [...this.lipShapes[phoneme]];
            
            // 根据音量调整强度
            const intensity = Math.min(volume * 2, 1.0);
            for (let i = 0; i < this.targetShape.length; i++) {
                this.targetShape[i] *= intensity;
            }
        }
        
        // 平滑过渡到目标形状
        for (let i = 0; i < this.currentShape.length; i++) {
            this.currentShape[i] += (this.targetShape[i] - this.currentShape[i]) * this.smoothingFactor;
        }
    }
    
    /**
     * 获取当前嘴唇形状参数
     */
    getLipShapeArray() {
        // 返回12个元素的数组，前6个是嘴唇参数，后6个保持原值
        const result = new Float32Array(12);
        
        // 设置嘴唇参数
        for (let i = 0; i < 6; i++) {
            result[i] = this.currentShape[i];
        }
        
        // 后6个参数保持默认值或从原系统获取
        for (let i = 6; i < 12; i++) {
            result[i] = 0.0;
        }
        
        return result;
    }
    
    /**
     * 启动嘴唇同步
     */
    async start() {
        if (this.isActive) return;
        
        console.log('🚀 启动自定义嘴唇同步');
        
        const success = await this.initAudio();
        if (!success) {
            console.error('❌ 无法启动嘴唇同步');
            return false;
        }
        
        this.isActive = true;
        this.animate();
        
        console.log('✅ 自定义嘴唇同步已启动');
        return true;
    }
    
    /**
     * 停止嘴唇同步
     */
    stop() {
        console.log('⏹️ 停止自定义嘴唇同步');
        
        this.isActive = false;
        
        if (this.microphone) {
            this.microphone.disconnect();
        }
        
        if (this.audioContext) {
            this.audioContext.close();
        }
        
        // 重置嘴唇形状
        this.currentShape = [...this.lipShapes.silence];
        this.targetShape = [...this.lipShapes.silence];
    }
    
    /**
     * 动画循环
     */
    animate() {
        if (!this.isActive) return;
        
        // 分析音频
        this.analyzeAudio();
        
        // 继续下一帧
        requestAnimationFrame(() => this.animate());
    }
    
    /**
     * 获取调试信息
     */
    getDebugInfo() {
        return {
            isActive: this.isActive,
            volume: this.lipSyncData.volume.toFixed(3),
            frequency: this.lipSyncData.frequency.toFixed(1),
            phoneme: this.lipSyncData.phoneme,
            formants: this.lipSyncData.formants.length,
            currentShape: this.currentShape.map(v => v.toFixed(3))
        };
    }
}

// 创建全局实例
window.customLipSync = new CustomLipSync();

// 添加控制函数
window.lipSyncControl = {
    start: () => window.customLipSync.start(),
    stop: () => window.customLipSync.stop(),
    debug: () => console.log('🔍 嘴唇同步调试信息:', window.customLipSync.getDebugInfo()),
    getShape: () => window.customLipSync.getLipShapeArray()
};

console.log('🎤 自定义嘴唇同步系统已加载');
console.log('💡 使用方法:');
console.log('  lipSyncControl.start() - 启动嘴唇同步');
console.log('  lipSyncControl.stop() - 停止嘴唇同步');
console.log('  lipSyncControl.debug() - 查看调试信息');
console.log('  lipSyncControl.getShape() - 获取当前嘴唇形状');
