/**
 * WASM模块加载助手
 * 提供更好的WASM模块加载检测和等待机制
 */

console.log('🔧 WASM加载助手已加载');

window.WasmLoaderHelper = {
    isReady: false,
    callbacks: [],
    checkInterval: null
};

/**
 * 检查WASM模块是否完全准备就绪
 */
function checkWasmReady() {
    const hasModule = typeof Module !== 'undefined';
    const hasHeap = hasModule && Module.HEAPU8;
    const hasUpdateBlendShape = hasModule && typeof Module._updateBlendShape === 'function';
    const hasProcessImage = hasModule && typeof Module._processImage === 'function';
    const hasMalloc = hasModule && typeof Module._malloc === 'function';
    
    const isReady = hasModule && hasHeap && hasUpdateBlendShape && hasProcessImage && hasMalloc;
    
    if (isReady && !window.WasmLoaderHelper.isReady) {
        console.log('✅ WASM模块完全准备就绪');
        console.log('📊 WASM模块信息:', {
            memorySize: Module.HEAPU8.length,
            hasUpdateBlendShape: !!Module._updateBlendShape,
            hasProcessImage: !!Module._processImage,
            hasMalloc: !!Module._malloc,
            hasFree: !!Module._free
        });
        
        window.WasmLoaderHelper.isReady = true;
        
        // 执行所有等待的回调
        window.WasmLoaderHelper.callbacks.forEach(callback => {
            try {
                callback();
            } catch (error) {
                console.error('❌ WASM就绪回调执行失败:', error);
            }
        });
        
        // 清空回调列表
        window.WasmLoaderHelper.callbacks = [];
        
        // 停止检查
        if (window.WasmLoaderHelper.checkInterval) {
            clearInterval(window.WasmLoaderHelper.checkInterval);
            window.WasmLoaderHelper.checkInterval = null;
        }
    }
    
    return isReady;
}

/**
 * 等待WASM模块准备就绪
 */
function waitForWasm() {
    return new Promise((resolve) => {
        if (window.WasmLoaderHelper.isReady) {
            resolve(Module);
            return;
        }
        
        // 添加到回调列表
        window.WasmLoaderHelper.callbacks.push(() => resolve(Module));
        
        // 如果还没有开始检查，启动检查
        if (!window.WasmLoaderHelper.checkInterval) {
            console.log('⏳ 开始等待WASM模块加载...');
            window.WasmLoaderHelper.checkInterval = setInterval(checkWasmReady, 200);
        }
    });
}

/**
 * 当WASM准备就绪时执行回调
 */
function onWasmReady(callback) {
    if (window.WasmLoaderHelper.isReady) {
        callback();
    } else {
        window.WasmLoaderHelper.callbacks.push(callback);
        
        // 启动检查
        if (!window.WasmLoaderHelper.checkInterval) {
            console.log('⏳ 开始监控WASM模块加载...');
            window.WasmLoaderHelper.checkInterval = setInterval(checkWasmReady, 200);
        }
    }
}

/**
 * 获取WASM模块状态
 */
function getWasmStatus() {
    const hasModule = typeof Module !== 'undefined';
    
    if (!hasModule) {
        return {
            ready: false,
            status: 'Module对象未定义',
            details: {
                hasModule: false,
                hasHeap: false,
                hasUpdateBlendShape: false,
                hasProcessImage: false,
                hasMalloc: false
            }
        };
    }
    
    const hasHeap = !!Module.HEAPU8;
    const hasUpdateBlendShape = typeof Module._updateBlendShape === 'function';
    const hasProcessImage = typeof Module._processImage === 'function';
    const hasMalloc = typeof Module._malloc === 'function';
    
    const ready = hasHeap && hasUpdateBlendShape && hasProcessImage && hasMalloc;
    
    return {
        ready,
        status: ready ? '完全准备就绪' : '部分加载',
        details: {
            hasModule: true,
            hasHeap,
            hasUpdateBlendShape,
            hasProcessImage,
            hasMalloc,
            memorySize: hasHeap ? Module.HEAPU8.length : 0
        }
    };
}

/**
 * 创建WASM状态监控面板
 */
function createWasmStatusPanel() {
    // 检查是否已存在
    if (document.getElementById('wasmStatusPanel')) {
        return;
    }
    
    const panel = document.createElement('div');
    panel.id = 'wasmStatusPanel';
    panel.style.cssText = `
        position: fixed;
        bottom: 10px;
        right: 10px;
        background: rgba(0, 0, 0, 0.9);
        color: white;
        padding: 10px;
        border-radius: 8px;
        font-family: monospace;
        font-size: 11px;
        z-index: 10001;
        min-width: 200px;
        max-width: 300px;
    `;
    
    panel.innerHTML = `
        <h4 style="margin: 0 0 8px 0; color: #ffd700;">🔧 WASM状态</h4>
        <div id="wasmStatusContent">检测中...</div>
        <button id="wasmStatusToggle" style="margin-top: 8px; padding: 3px 8px; background: #333; color: white; border: 1px solid #666; border-radius: 3px; cursor: pointer; font-size: 10px;">隐藏</button>
    `;
    
    document.body.appendChild(panel);
    
    // 绑定切换按钮
    document.getElementById('wasmStatusToggle').onclick = () => {
        const content = document.getElementById('wasmStatusContent');
        const button = document.getElementById('wasmStatusToggle');
        
        if (content.style.display === 'none') {
            content.style.display = 'block';
            button.textContent = '隐藏';
        } else {
            content.style.display = 'none';
            button.textContent = '显示';
        }
    };
    
    // 定期更新状态
    setInterval(updateWasmStatusPanel, 500);
}

/**
 * 更新WASM状态面板
 */
function updateWasmStatusPanel() {
    const content = document.getElementById('wasmStatusContent');
    if (!content) return;
    
    const status = getWasmStatus();
    const statusColor = status.ready ? '#28a745' : '#ffc107';
    
    content.innerHTML = `
        <div style="color: ${statusColor};">状态: ${status.status}</div>
        <div style="margin-top: 5px; font-size: 10px; color: #ccc;">
            Module: ${status.details.hasModule ? '✅' : '❌'}<br>
            内存: ${status.details.hasHeap ? '✅' : '❌'} ${status.details.memorySize ? `(${status.details.memorySize} bytes)` : ''}<br>
            BlendShape: ${status.details.hasUpdateBlendShape ? '✅' : '❌'}<br>
            ProcessImage: ${status.details.hasProcessImage ? '✅' : '❌'}<br>
            Malloc: ${status.details.hasMalloc ? '✅' : '❌'}
        </div>
    `;
}

// 导出全局接口
window.wasmHelper = {
    waitForWasm,
    onWasmReady,
    getStatus: getWasmStatus,
    isReady: () => window.WasmLoaderHelper.isReady,
    createStatusPanel: createWasmStatusPanel
};

// 立即开始检查
checkWasmReady();

// 页面加载完成后创建状态面板
window.addEventListener('load', () => {
    setTimeout(createWasmStatusPanel, 1000);
});

// 监听Module对象的变化
if (typeof Module !== 'undefined') {
    console.log('🎯 检测到Module对象，开始监控');
    
    // 如果Module有ready Promise，监听它
    if (Module.ready && typeof Module.ready.then === 'function') {
        Module.ready.then(() => {
            console.log('🎉 Module.ready Promise已解决');
            setTimeout(checkWasmReady, 100);
        });
    }
}

console.log('💡 WASM加载助手控制命令:');
console.log('  wasmHelper.waitForWasm() - 等待WASM准备就绪');
console.log('  wasmHelper.getStatus() - 获取WASM状态');
console.log('  wasmHelper.isReady() - 检查是否准备就绪');
console.log('  wasmHelper.createStatusPanel() - 创建状态面板');
