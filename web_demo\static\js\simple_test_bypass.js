/**
 * 最简单的测试绕过方案
 * 先确保能正确拦截函数，再逐步优化
 */

console.log('🔧 简单测试绕过脚本已加载');

// 等待页面完全加载
window.addEventListener('load', function() {
    console.log('📋 页面加载完成，开始检测WASM模块...');
    
    // 检测WASM模块的函数
    function checkWasmModule() {
        if (typeof Module !== 'undefined') {
            console.log('✅ Module对象已存在');
            
            if (Module._processJson) {
                console.log('✅ processJson函数已存在');
            } else {
                console.log('❌ processJson函数不存在');
            }
            
            if (Module._processImage) {
                console.log('✅ processImage函数已存在');
            } else {
                console.log('❌ processImage函数不存在');
            }
            
            // 如果函数存在，尝试简单拦截
            if (Module._processJson && Module._processImage) {
                installSimpleIntercept();
            } else {
                console.log('⏳ WASM函数未就绪，1秒后重试...');
                setTimeout(checkWasmModule, 1000);
            }
        } else {
            console.log('⏳ Module对象未就绪，1秒后重试...');
            setTimeout(checkWasmModule, 1000);
        }
    }
    
    // 安装简单拦截
    function installSimpleIntercept() {
        console.log('🚀 开始安装简单拦截器...');
        
        // 保存原始函数
        const originalProcessJson = Module._processJson;
        const originalProcessImage = Module._processImage;
        
        // 拦截processJson - 只记录调用，不修改任何东西
        Module._processJson = function() {
            console.log('🎯 processJson被调用了！参数数量:', arguments.length);
            
            // 尝试读取JSON数据
            if (arguments.length > 0) {
                try {
                    const jsonPtr = arguments[0];
                    const jsonString = Module.UTF8ToString(jsonPtr);
                    console.log('📄 JSON数据长度:', jsonString.length);
                    console.log('📄 JSON前100字符:', jsonString.substring(0, 100));
                    
                    // 检查是否包含authorized字段
                    if (jsonString.includes('authorized')) {
                        console.log('🔍 发现authorized字段！');
                        
                        // 尝试解析JSON
                        const jsonData = JSON.parse(jsonString);
                        console.log('📊 authorized当前值:', jsonData.authorized);
                        
                        // 修改authorized为true
                        jsonData.authorized = true;
                        console.log('✅ 已设置authorized=true');
                        
                        // 重新写入
                        const modifiedJson = JSON.stringify(jsonData);
                        Module.stringToUTF8(modifiedJson, jsonPtr, modifiedJson.length + 1);
                        console.log('💾 已写回修改后的JSON');
                    }
                } catch (error) {
                    console.log('❌ JSON处理出错:', error.message);
                }
            }
            
            // 调用原始函数
            return originalProcessJson.apply(this, arguments);
        };
        
        // 拦截processImage - 只记录调用，不修改任何东西
        Module._processImage = function() {
            console.log('🖼️ processImage被调用了！参数:', Array.from(arguments));
            
            // 调用原始函数
            return originalProcessImage.apply(this, arguments);
        };
        
        console.log('✅ 简单拦截器安装完成');
        
        // 添加全局控制函数
        window.testBypass = {
            status: function() {
                console.log('📊 绕过状态: 已安装');
                console.log('📊 Module存在:', typeof Module !== 'undefined');
                console.log('📊 processJson存在:', typeof Module._processJson === 'function');
                console.log('📊 processImage存在:', typeof Module._processImage === 'function');
            },
            
            restore: function() {
                Module._processJson = originalProcessJson;
                Module._processImage = originalProcessImage;
                console.log('🔄 已恢复原始函数');
            }
        };
        
        console.log('💡 可以在控制台使用 testBypass.status() 查看状态');
        console.log('💡 可以在控制台使用 testBypass.restore() 恢复原始函数');
    }
    
    // 开始检测
    setTimeout(checkWasmModule, 1000);
});

// 监听WASM模块加载事件
if (typeof Module !== 'undefined' && Module.ready) {
    Module.ready.then(() => {
        console.log('🎉 WASM模块ready事件触发');
    });
}

// 定期检查Module状态
setInterval(() => {
    if (typeof Module !== 'undefined' && Module._processJson && Module._processImage) {
        if (!window.interceptInstalled) {
            console.log('🔄 检测到WASM函数可用，准备安装拦截器...');
            window.interceptInstalled = true;
        }
    }
}, 2000);
