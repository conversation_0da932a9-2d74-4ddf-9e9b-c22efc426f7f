/**
 * 基于Canvas渲染流程的精确绕过方案
 * 分析发现：processImage在第651行被调用，处理128x128的图像数据
 * 这个函数同时处理logo和嘴唇渲染，我们需要更精确的控制
 */

window.CanvasBypass = {
    isActive: false,
    originalProcessImage: null,
    originalProcessJson: null,
    debugMode: true
};

function logCanvas(message, type = 'info') {
    if (!window.CanvasBypass.debugMode) return;
    
    const prefix = '🎨 Canvas绕过';
    const styles = {
        info: 'color: #2196F3',
        success: 'color: #4CAF50',
        warning: 'color: #FF9800',
        error: 'color: #F44336'
    };
    
    console.log(`%c${prefix}: ${message}`, styles[type] || styles.info);
}

/**
 * 智能图像处理 - 保留嘴唇，去除logo
 */
function intelligentImageProcessing(imageDataPtr, inputWidth, inputHeight, imageDataGlPtr, outputWidth, outputHeight) {
    logCanvas(`图像处理调用: ${inputWidth}x${inputHeight} -> ${outputWidth}x${outputHeight}`);
    
    try {
        // 先调用原始函数进行完整渲染（包括嘴唇）
        const result = window.CanvasBypass.originalProcessImage(
            imageDataPtr, inputWidth, inputHeight, imageDataGlPtr, outputWidth, outputHeight
        );
        
        // 后处理：从渲染结果中移除logo，但保留嘴唇
        postProcessRemoveLogo(imageDataGlPtr, outputWidth, outputHeight);
        
        logCanvas('图像处理完成 - 保留嘴唇，移除logo', 'success');
        return result;
        
    } catch (error) {
        logCanvas(`图像处理失败: ${error.message}`, 'error');
        // 出错时直接返回原始处理结果
        return window.CanvasBypass.originalProcessImage(
            imageDataPtr, inputWidth, inputHeight, imageDataGlPtr, outputWidth, outputHeight
        );
    }
}

/**
 * 后处理移除logo
 */
function postProcessRemoveLogo(imageDataPtr, width, height) {
    try {
        const imageData = new Uint8Array(Module.HEAPU8.buffer, imageDataPtr, width * height * 4);
        
        // 分析图像，找出logo区域
        const logoRegions = detectLogoRegions(imageData, width, height);
        
        if (logoRegions.length > 0) {
            logCanvas(`检测到${logoRegions.length}个logo区域`, 'info');
            
            // 移除每个logo区域
            logoRegions.forEach((region, index) => {
                removeLogoFromRegion(imageData, width, height, region);
                logCanvas(`移除logo区域${index + 1}: (${region.x},${region.y}) ${region.w}x${region.h}`, 'success');
            });
        } else {
            logCanvas('未检测到logo区域', 'info');
        }
        
    } catch (error) {
        logCanvas(`后处理失败: ${error.message}`, 'warning');
    }
}

/**
 * 检测logo区域
 */
function detectLogoRegions(imageData, width, height) {
    const regions = [];
    
    // 定义可能的logo位置（基于128x128的图像）
    const candidateRegions = [
        // 右下角
        { x: Math.floor(width * 0.6), y: Math.floor(height * 0.6), w: Math.floor(width * 0.4), h: Math.floor(height * 0.4) },
        // 右上角
        { x: Math.floor(width * 0.6), y: 0, w: Math.floor(width * 0.4), h: Math.floor(height * 0.4) },
        // 左下角
        { x: 0, y: Math.floor(height * 0.6), w: Math.floor(width * 0.4), h: Math.floor(height * 0.4) },
        // 中心区域（可能的水印位置）
        { x: Math.floor(width * 0.3), y: Math.floor(height * 0.3), w: Math.floor(width * 0.4), h: Math.floor(height * 0.4) }
    ];
    
    for (const region of candidateRegions) {
        if (isLogoRegion(imageData, width, height, region)) {
            regions.push(region);
        }
    }
    
    return regions;
}

/**
 * 判断区域是否包含logo
 */
function isLogoRegion(imageData, width, height, region) {
    let logoPixelCount = 0;
    let totalPixels = 0;
    let highContrastPixels = 0;
    
    for (let y = region.y; y < region.y + region.h && y < height; y++) {
        for (let x = region.x; x < region.x + region.w && x < width; x++) {
            const index = (y * width + x) * 4;
            const r = imageData[index];
            const g = imageData[index + 1];
            const b = imageData[index + 2];
            const a = imageData[index + 3];
            
            totalPixels++;
            
            // 检测logo特征
            const brightness = (r + g + b) / 3;
            const isHighContrast = (brightness > 200 || brightness < 50) && a > 200;
            
            if (isHighContrast) {
                highContrastPixels++;
            }
            
            // 检测典型的logo颜色（白色、黑色、特定品牌色）
            if (a > 200 && (
                (r > 200 && g > 200 && b > 200) || // 白色
                (r < 50 && g < 50 && b < 50) ||     // 黑色
                (r > 200 && g < 100 && b < 100) ||  // 红色
                (r < 100 && g < 100 && b > 200)     // 蓝色
            )) {
                logoPixelCount++;
            }
        }
    }
    
    const logoRatio = logoPixelCount / totalPixels;
    const contrastRatio = highContrastPixels / totalPixels;
    
    // 如果区域内有足够的logo特征像素，认为是logo区域
    return logoRatio > 0.05 || contrastRatio > 0.3;
}

/**
 * 从区域移除logo
 */
function removeLogoFromRegion(imageData, width, height, region) {
    try {
        // 获取区域周围的背景色
        const backgroundColor = getRegionBackgroundColor(imageData, width, height, region);
        
        for (let y = region.y; y < region.y + region.h && y < height; y++) {
            for (let x = region.x; x < region.x + region.w && x < width; x++) {
                const index = (y * width + x) * 4;
                const r = imageData[index];
                const g = imageData[index + 1];
                const b = imageData[index + 2];
                const a = imageData[index + 3];
                
                // 只处理可能是logo的像素
                const brightness = (r + g + b) / 3;
                const isLogoPixel = a > 200 && (
                    (r > 200 && g > 200 && b > 200) ||
                    (r < 50 && g < 50 && b < 50) ||
                    (brightness > 200 || brightness < 50)
                );
                
                if (isLogoPixel) {
                    // 用背景色替换logo像素
                    imageData[index] = backgroundColor.r;
                    imageData[index + 1] = backgroundColor.g;
                    imageData[index + 2] = backgroundColor.b;
                    imageData[index + 3] = backgroundColor.a;
                }
            }
        }
    } catch (error) {
        logCanvas(`区域logo移除失败: ${error.message}`, 'warning');
    }
}

/**
 * 获取区域的背景色
 */
function getRegionBackgroundColor(imageData, width, height, region) {
    let totalR = 0, totalG = 0, totalB = 0, totalA = 0;
    let count = 0;
    
    // 采样区域周围的像素来确定背景色
    const sampleRadius = 5;
    const samplePositions = [
        { x: region.x - sampleRadius, y: region.y - sampleRadius },
        { x: region.x + region.w + sampleRadius, y: region.y - sampleRadius },
        { x: region.x - sampleRadius, y: region.y + region.h + sampleRadius },
        { x: region.x + region.w + sampleRadius, y: region.y + region.h + sampleRadius }
    ];
    
    for (const pos of samplePositions) {
        if (pos.x >= 0 && pos.x < width && pos.y >= 0 && pos.y < height) {
            const index = (pos.y * width + pos.x) * 4;
            totalR += imageData[index];
            totalG += imageData[index + 1];
            totalB += imageData[index + 2];
            totalA += imageData[index + 3];
            count++;
        }
    }
    
    if (count === 0) {
        // 如果无法采样周围像素，使用默认的肤色
        return { r: 220, g: 180, b: 140, a: 255 };
    }
    
    return {
        r: Math.floor(totalR / count),
        g: Math.floor(totalG / count),
        b: Math.floor(totalB / count),
        a: Math.floor(totalA / count)
    };
}

/**
 * 安装Canvas绕过拦截器
 */
function installCanvasBypass() {
    if (!Module || !Module._processImage || !Module._processJson) {
        logCanvas('等待WASM模块加载...', 'warning');
        setTimeout(installCanvasBypass, 100);
        return;
    }
    
    if (window.CanvasBypass.isActive) {
        logCanvas('Canvas绕过已经激活', 'warning');
        return;
    }
    
    logCanvas('安装Canvas绕过拦截器', 'info');
    
    // 保存原始函数
    window.CanvasBypass.originalProcessImage = Module._processImage;
    window.CanvasBypass.originalProcessJson = Module._processJson;
    
    // 只修改JSON授权，不影响其他数据
    Module._processJson = function(jsonPtr) {
        logCanvas('拦截processJson - 设置授权状态', 'info');
        
        try {
            const jsonString = Module.UTF8ToString(jsonPtr);
            const jsonData = JSON.parse(jsonString);
            
            // 只修改授权字段
            jsonData.authorized = true;
            
            const modifiedJsonString = JSON.stringify(jsonData);
            const newLength = new TextEncoder().encode(modifiedJsonString).length + 1;
            const originalLength = new TextEncoder().encode(jsonString).length + 1;
            
            if (newLength <= originalLength) {
                Module.stringToUTF8(modifiedJsonString, jsonPtr, newLength);
                return window.CanvasBypass.originalProcessJson(jsonPtr);
            } else {
                const newPtr = Module._malloc(newLength);
                Module.stringToUTF8(modifiedJsonString, newPtr, newLength);
                const result = window.CanvasBypass.originalProcessJson(newPtr);
                Module._free(newPtr);
                return result;
            }
            
        } catch (error) {
            logCanvas(`processJson处理失败: ${error.message}`, 'error');
            return window.CanvasBypass.originalProcessJson(jsonPtr);
        }
    };
    
    // 智能图像处理
    Module._processImage = intelligentImageProcessing;
    
    window.CanvasBypass.isActive = true;
    logCanvas('Canvas绕过安装完成 - 策略：完整渲染+后处理去logo', 'success');
}

// 控制接口
window.CanvasBypass.install = installCanvasBypass;
window.CanvasBypass.uninstall = () => {
    if (window.CanvasBypass.isActive) {
        Module._processImage = window.CanvasBypass.originalProcessImage;
        Module._processJson = window.CanvasBypass.originalProcessJson;
        window.CanvasBypass.isActive = false;
        logCanvas('Canvas绕过已卸载', 'info');
    }
};

window.CanvasBypass.status = () => ({
    active: window.CanvasBypass.isActive,
    hasOriginals: !!(window.CanvasBypass.originalProcessImage && window.CanvasBypass.originalProcessJson)
});

// 自动安装
logCanvas('Canvas绕过系统已加载', 'success');
logCanvas('策略：让WASM完整渲染（包括嘴唇），然后后处理移除logo', 'info');

if (typeof Module !== 'undefined' && Module.ready) {
    Module.ready.then(() => {
        setTimeout(installCanvasBypass, 500);
    });
} else {
    const checkModule = setInterval(() => {
        if (typeof Module !== 'undefined' && Module.ready) {
            clearInterval(checkModule);
            Module.ready.then(() => {
                setTimeout(installCanvasBypass, 500);
            });
        }
    }, 100);
}
